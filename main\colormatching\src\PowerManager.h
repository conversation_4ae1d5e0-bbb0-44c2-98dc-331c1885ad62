#ifndef POWER_MANAGER_H
#define POWER_MANAGER_H

#include <Arduino.h>
#include "HardwareConfig.h"

class PowerManager {
public:
    enum ButtonEvent {
        BUTTON_NONE,
        BUTTON_SINGLE_PRESS,
        BUTTON_DOUBLE_PRESS,
        BUTTON_LONG_PRESS
    };

    enum PowerState {
        POWER_OFF,
        POWER_ON,
        POWER_SHUTTING_DOWN
    };

    PowerManager();
    bool begin();
    void update();
    
    // Power control
    bool isPoweredOn() const { return currentState == POWER_ON; }
    void powerOn();
    void powerOff();
    void emergencyShutdown();
    
    // Button events
    ButtonEvent getLastButtonEvent();
    bool hasButtonEvent() const { return lastButtonEvent != BUTTON_NONE; }
    void clearButtonEvent() { lastButtonEvent = BUTTON_NONE; }
    
    // Buzzer control
    void beep(uint16_t duration_ms = BUZZER_BEEP_DURATION_MS);
    void beepPattern(uint8_t count, uint16_t duration_ms = 100, uint16_t interval_ms = 100);
    
    // Power monitoring
    PowerState getPowerState() const { return currentState; }
    uint32_t getUptimeMs() const { return millis() - powerOnTime; }

private:
    PowerState currentState;
    ButtonEvent lastButtonEvent;
    
    // Button state tracking
    bool buttonPressed;
    bool lastButtonState;
    uint32_t buttonPressTime;
    uint32_t buttonReleaseTime;
    uint32_t lastDebounceTime;
    uint8_t clickCount;
    bool longPressDetected;
    
    // Power timing
    uint32_t powerOnTime;
    uint32_t shutdownStartTime;
    
    // Buzzer state
    bool buzzerActive;
    uint32_t buzzerStartTime;
    uint16_t buzzerDuration;
    uint8_t buzzerBeepCount;
    uint8_t buzzerCurrentBeep;
    uint16_t buzzerBeepDuration;
    uint16_t buzzerBeepInterval;
    uint32_t buzzerLastBeepTime;
    
    // Private methods
    void updateButtonState();
    void processButtonEvents();
    void updateBuzzer();
    void setBuzzer(bool state);
    void setSysEnable(bool state);
    bool readPowerButton();
    
    // Button timing helpers
    bool isButtonPressed();
    bool wasButtonReleased();
    uint32_t getPressDuration();
    uint32_t getTimeSinceRelease();
};

#endif // POWER_MANAGER_H
