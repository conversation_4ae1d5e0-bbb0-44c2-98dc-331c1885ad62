#ifndef TFT_DISPLAY_H
#define TFT_DISPLAY_H

#include <Adafruit_GFX.h>
#include <Adafruit_ST7789.h>
#include "DisplayBuffer.h"
#include "ButtonHandler.h"

class TFTDisplay {
public:
    TFTDisplay();
    void begin();
    void showMessage(const char* message);
    void showError(const char* message);
    void showTitle(const char* title);
    void updateLiveReading(float X, float Y, float Z, 
                          float x, float y, float Y_luminance,
                          uint8_t r, uint8_t g, uint8_t b,
                          const char* hex, const char* closestName,
                          float deltaE, bool isVividWhite);
    int handleMenu();

private:
    void displayMenu();
    
    Adafruit_ST7789 tft;
    DisplayBuffer displayBuffer;
    ButtonHandler buttonSelect;
    ButtonHandler buttonUp;
    ButtonHandler buttonDown;
    int menuIndex;
    bool inMenu;
};

#endif // TFT_DISPLAY_H
