#include "ColorMatchingApp.h"

#define RGB_LED_PIN 21
#define RGB_LED_COUNT 1

ColorMatchingApp::ColorMatchingApp() : 
    rgbLed(RGB_LED_COUNT, RGB_LED_PIN, NEO_GRB + NEO_KHZ800),
    currentState(STATE_LIVE_READING),
    bleConnected(false),
    pServer(nullptr),
    pCharacteristic(nullptr)
{
}

bool ColorMatchingApp::begin() {
    // Initialize display first since we need it for error messages
    display.begin();
    
    // Initialize color sensor
    if (!colorSensor.begin()) {
        display.showError("Sensor Init Failed!");
        return false;
    }

    // Initialize RGB LED
    rgbLed.begin();
    rgbLed.setBrightness(50);
    rgbLed.show();

    // Initialize storage
    if (!LittleFS.begin(true)) {  // Use true for format_if_failed
        display.showError("Storage Init Failed!");
        return false;
    }

    // Load calibration data
    loadCalibration();
    display.showMessage("System Ready");
    return true;
}

void ColorMatchingApp::update() {
    handleUserInput();

    switch (currentState) {
        case STATE_LIVE_READING:
            if (colorSensor.update()) {
                processColorData();
                updateDisplay();
            }
            break;

        case STATE_CALIBRATING:
            colorSensor.calibrate();
            saveCalibration();
            currentState = STATE_LIVE_READING;
            break;

        case STATE_BLE_SETUP:
            setupBLE();
            currentState = STATE_LIVE_READING;
            break;

        case STATE_SENDING_DATA:
            if (bleConnected) {
                const ColorSensor::ColorData& data = colorSensor.getData();
                // Format and send data via BLE
                char buffer[64];
                snprintf(buffer, sizeof(buffer), "X:%.2f,Y:%.2f,Z:%.2f", 
                        data.X, data.Y, data.Z);
                pCharacteristic->setValue(buffer);
                pCharacteristic->notify();
            }
            currentState = STATE_LIVE_READING;
            break;

        case STATE_SAVING_DATA:
            saveReading();
            currentState = STATE_LIVE_READING;
            break;
    }

    if (bleConnected) {
        handleBLE();
    }
}

void ColorMatchingApp::processColorData() {
    const ColorSensor::ColorData& data = colorSensor.getData();
    
    // Update RGB LED to match reading
    uint8_t r = data.r;
    uint8_t g = data.g;
    uint8_t b = data.b;
    rgbLed.setPixelColor(0, rgbLed.Color(r, g, b));
    rgbLed.show();
}

void ColorMatchingApp::updateDisplay() {
    const ColorSensor::ColorData& data = colorSensor.getData();
    display.updateLiveReading(
        data.X, data.Y, data.Z,
        data.x, data.y, data.Y_luminance,
        data.r, data.g, data.b,
        data.hex, data.closestName,
        data.deltaE, data.isVividWhite
    );
}

void ColorMatchingApp::handleUserInput() {
    int menuAction = display.handleMenu();
    switch (menuAction) {
        case 1: // Calibrate
            currentState = STATE_CALIBRATING;
            break;
        case 2: // Setup BT
            currentState = STATE_BLE_SETUP;
            break;
        case 3: // Send Data
            currentState = STATE_SENDING_DATA;
            break;
        case 4: // Save Data
            currentState = STATE_SAVING_DATA;
            break;
    }
}

void ColorMatchingApp::setupBLE() {
    BLEDevice::init("ColorMatcher");
    pServer = BLEDevice::createServer();
    BLEService *pService = pServer->createService(BLEUUID("180F"));
    pCharacteristic = pService->createCharacteristic(
        BLEUUID("2A19"),
        BLECharacteristic::PROPERTY_READ |
        BLECharacteristic::PROPERTY_NOTIFY
    );
    pService->start();
    BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
    pAdvertising->addServiceUUID(BLEUUID("180F"));
    pAdvertising->setScanResponse(true);
    BLEDevice::startAdvertising();
}

void ColorMatchingApp::handleBLE() {
    // Handle BLE events and data transfer
    if (pServer->getConnectedCount() > 0) {
        bleConnected = true;
    } else {
        bleConnected = false;
    }
}

void ColorMatchingApp::saveReading() {
    const ColorSensor::ColorData& data = colorSensor.getData();
    File file = LittleFS.open("/readings.csv", "a");
    if (file) {
        char buffer[128];
        snprintf(buffer, sizeof(buffer), 
                "%.2f,%.2f,%.2f,%.4f,%.4f,%.2f,%s\n",
                data.X, data.Y, data.Z,
                data.x, data.y, data.deltaE,
                data.closestName);
        file.print(buffer);
        file.close();
        display.showMessage("Reading Saved");
    } else {
        display.showError("Save Failed!");
    }
}

void ColorMatchingApp::loadCalibration() {
    File file = LittleFS.open("/calibration.dat", "r");
    if (file) {
        // Load calibration data
        file.close();
    }
}

void ColorMatchingApp::saveCalibration() {
    File file = LittleFS.open("/calibration.dat", "w");
    if (file) {
        // Save calibration data
        file.close();
        display.showMessage("Calibration Saved");
    } else {
        display.showError("Cal Save Failed!");
    }
}
