{"configurations": [{"name": "ESP-IDF", "compilerPath": "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "compileCommands": "${workspaceFolder}/build/compile_commands.json", "includePath": ["${workspaceFolder}/main", "${workspaceFolder}/components/**", "${workspaceFolder}/build/config", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\**", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\freertos\\**", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\esp_common\\include", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\esp_hw_support\\include", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\soc\\esp32s3\\include", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\hal\\esp32s3\\include", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components\\esp_system\\include"], "browse": {"path": ["${workspaceFolder}", "C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\components"], "limitSymbolsToIncludedHeaders": false}, "defines": ["ESP_PLATFORM", "IDF_VER=\"v5.4.1\"", "CONFIG_IDF_TARGET_ESP32S3=1", "CONFIG_IDF_TARGET=\"esp32s3\"", "CONFIG_FREERTOS_HZ=1000"], "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "gcc-x64"}], "version": 4}