CMake Deprecation Warning at CMakeLists.txt:3 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at C:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/CMakeLists.txt:21 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Error at C:/Espressif/frameworks/esp-idf-v5.4.1/tools/cmake/gdbinit.cmake:40 (file):
  file FILE([TO_CMAKE_PATH|TO_NATIVE_PATH] path result) must be called with
  exactly three arguments.
Call Stack (most recent call first):
  C:/Espressif/frameworks/esp-idf-v5.4.1/tools/cmake/project.cmake:356 (__generate_gdbinit)
  C:/Espressif/frameworks/esp-idf-v5.4.1/tools/cmake/project.cmake:923 (__project_info)
  CMakeLists.txt:6 (project)


