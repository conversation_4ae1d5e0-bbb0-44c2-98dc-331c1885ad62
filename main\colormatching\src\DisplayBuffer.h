#ifndef DISPLAY_BUFFER_H
#define DISPLAY_BUFFER_H

#include <Arduino.h>
#include "ColorSensor.h"

class DisplayBuffer {
public:
    static const unsigned long UPDATE_INTERVAL = 250; // 250ms between updates
    
    DisplayBuffer() : lastUpdateTime(0) {
        memset(&currentData, 0, sizeof(ColorSensor::ColorData));
        memset(&previousData, 0, sizeof(ColorSensor::ColorData));
    }
    
    bool shouldUpdate(const ColorSensor::ColorData& newData) {
        unsigned long currentTime = millis();
        
        // Enforce minimum update interval
        if (currentTime - lastUpdateTime < UPDATE_INTERVAL) {
            return false;
        }
        
        // Check if data has changed
        if (newData == previousData) {
            return false;
        }
        
        // Update stored data and time
        previousData = currentData;
        currentData = newData;
        lastUpdateTime = currentTime;
        return true;
    }
    
    const ColorSensor::ColorData& getCurrentData() const { return currentData; }
    const ColorSensor::ColorData& getPreviousData() const { return previousData; }

private:
    ColorSensor::ColorData currentData;
    ColorSensor::ColorData previousData;
    unsigned long lastUpdateTime;
};

#endif // DISPLAY_BUFFER_H
