{"version": "2.0.0", "tasks": [{"label": "Build - Build project", "type": "shell", "command": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env\\Scripts\\python.exe", "args": ["C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\idf.py", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"IDF_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF", "IDF_TOOLS_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools", "IDF_PYTHON_ENV_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env", "PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env\\Scripts;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\ninja\\1.12.1;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\cmake\\3.30.2\\bin;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;${env:PATH}"}}, "problemMatcher": ["$gcc"]}, {"label": "Flash - Flash to device", "type": "shell", "command": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env\\Scripts\\python.exe", "args": ["C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\idf.py", "-p", "COM3", "flash"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"IDF_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF", "IDF_TOOLS_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools", "IDF_PYTHON_ENV_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env", "PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env\\Scripts;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\ninja\\1.12.1;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\cmake\\3.30.2\\bin;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;${env:PATH}"}}, "problemMatcher": ["$gcc"], "dependsOn": "Build - Build project"}, {"label": "Monitor - Monitor device", "type": "shell", "command": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env\\Scripts\\python.exe", "args": ["C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\idf.py", "-p", "COM3", "monitor"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"IDF_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF", "IDF_TOOLS_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools", "IDF_PYTHON_ENV_PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env", "PATH": "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\python_env\\idf5.4_py3.11_env\\Scripts;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\ninja\\1.12.1;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\cmake\\3.30.2\\bin;C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;${env:PATH}"}}, "problemMatcher": []}]}