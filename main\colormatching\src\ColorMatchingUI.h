#ifndef COLOR_MATCHING_UI_H
#define COLOR_MATCHING_UI_H

#include <lvgl.h>
#include "ColorSensor.h"

class ColorMatchingUI {
public:
    ColorMatchingUI();
    bool begin();
    void update();
    void updateColorData(const ColorSensor::ColorData& data);
    void showMessage(const char* message);
    void showError(const char* error);
    
private:
    // Main screen objects
    lv_obj_t* mainScreen;
    lv_obj_t* colorPanel;
    lv_obj_t* dataPanel;
    lv_obj_t* statusPanel;
    
    // Color display
    lv_obj_t* colorRect;
    lv_obj_t* rgbLabel;
    lv_obj_t* hexLabel;
    
    // Data display
    lv_obj_t* xyzLabel;
    lv_obj_t* xyLabel;
    lv_obj_t* luminanceLabel;
    lv_obj_t* colorNameLabel;
    lv_obj_t* deltaELabel;
    
    // Status display
    lv_obj_t* statusLabel;
    lv_obj_t* messageLabel;
    
    // Buttons
    lv_obj_t* calibrateBtn;
    lv_obj_t* saveBtn;
    lv_obj_t* menuBtn;
    
    // Menu screen
    lv_obj_t* menuScreen;
    lv_obj_t* menuList;
    
    // Current screen state
    enum UIState {
        STATE_MAIN,
        STATE_MENU,
        STATE_CALIBRATION,
        STATE_SETTINGS
    };
    UIState currentState;
    
    // UI creation methods
    void createMainScreen();
    void createMenuScreen();
    void createColorPanel();
    void createDataPanel();
    void createStatusPanel();
    void createButtons();
    
    // Event handlers
    static void buttonEventHandler(lv_event_t* e);
    static void menuEventHandler(lv_event_t* e);
    
    // Helper methods
    void switchToScreen(lv_obj_t* screen);
    void updateColorDisplay(uint8_t r, uint8_t g, uint8_t b, const char* hex);
    void updateDataDisplay(float X, float Y, float Z, float x, float y, float luminance);
    void updateColorInfo(const char* colorName, float deltaE, bool isVividWhite);
    
    // Style objects
    lv_style_t colorPanelStyle;
    lv_style_t dataPanelStyle;
    lv_style_t buttonStyle;
    lv_style_t titleStyle;
};

#endif // COLOR_MATCHING_UI_H
