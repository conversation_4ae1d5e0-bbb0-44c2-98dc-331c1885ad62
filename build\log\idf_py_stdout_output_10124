-- IDF_TARGET is not set, guessed 'esp32s3' from sdkconfig 'C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig'
-- Found Git: C:/Program Files/Git/cmd/git.exe (found version "2.41.0.windows.3")
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Processing 2 dependencies:
NOTICE: [1/2] espressif/esp_jpeg (1.3.0)
NOTICE: [2/2] idf (5.4.1)
-- Project sdkconfig file C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig
Loading defaults file C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults...
C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults:28 CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240 was replaced with CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240 
C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults:29 CONFIG_ESP32S3_DEFAULT_CPU_FREQ_MHZ was replaced with CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ 
warning: unknown kconfig symbol 'ESP32_DEFAULT_CPU_FREQ_240' assigned to 'y' in C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32_DEFAULT_CPU_FREQ_MHZ' assigned to '240' in C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32S2_DEFAULT_CPU_FREQ_240' assigned to 'y' in C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32S2_DEFAULT_CPU_FREQ_MHZ' assigned to '240' in C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: C:/Espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "st7789" version: 1
-- Adding linker script C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Configuring incomplete, errors occurred!
