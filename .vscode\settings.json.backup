{"idf.adapterTargetName": "esp32s3", "idf.customExtraPaths": "C:\\Espressif\\python_env\\idf5.4_py3.11_env\\Scripts;C:\\Espressif\\tools\\ninja\\1.12.1;C:\\Espressif\\tools\\cmake\\3.30.2\\bin;C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin;C:\\Espressif\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin;C:\\Espressif\\tools\\esp32ulp-elf\\2.38_20240113\\esp32ulp-elf\\bin;C:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20241016\\openocd-esp32\\bin", "idf.customExtraVars": {"OPENOCD_SCRIPTS": "C:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20241016\\openocd-esp32\\share\\openocd\\scripts", "IDF_CCACHE_ENABLE": "1", "IDF_PATH": "C:\\Espressif\\frameworks\\esp-idf-v5.4.1", "IDF_TOOLS_PATH": "C:\\Espressif\\tools", "IDF_PYTHON_ENV_PATH": "C:\\Espressif\\python_env\\idf5.4_py3.11_env"}, "idf.espIdfPath": "C:\\Espressif\\frameworks\\esp-idf-v5.4.1", "idf.espIdfPathWin": "C:\\Espressif\\frameworks\\esp-idf-v5.4.1", "idf.pythonBinPath": "C:\\Espressif\\python_env\\idf5.4_py3.11_env\\Scripts\\python.exe", "idf.pythonBinPathWin": "C:\\Espressif\\python_env\\idf5.4_py3.11_env\\Scripts\\python.exe", "idf.toolsPath": "C:\\Espressif\\tools", "idf.toolsPathWin": "C:\\Espressif\\tools", "idf.gitPath": "C:\\Espressif\\tools\\idf-git\\2.44.0\\cmd\\git.exe", "idf.gitPathWin": "C:\\Espressif\\tools\\idf-git\\2.44.0\\cmd\\git.exe", "idf.buildPath": "${workspaceFolder}/build", "idf.buildPathWin": "${workspaceFolder}\\build", "idf.sdkconfigDefaults": "${workspaceFolder}/sdkconfig.defaults", "idf.sdkconfigDefaultsWin": "${workspaceFolder}\\sdkconfig.defaults", "idf.sdkconfigFilePath": "${workspaceFolder}/sdkconfig", "idf.sdkconfigFilePathWin": "${workspaceFolder}\\sdkconfig", "idf.cmakeCompilerArgs": ["-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\Pictures\\ESP-IDF\\tools\\cmake\\toolchain-esp32s3.cmake", "-DCMAKE_BUILD_TYPE=Debug"], "idf.enableCCache": true, "idf.enableIdfComponentManager": true, "idf.flashType": "UART", "idf.monitorBaudRate": "115200", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.port": "COM3", "idf.portWin": "COM3", "idf.projectName": "st7789", "idf.saveScope": "workspace", "cmake.sourceDirectory": "${workspaceFolder}", "cmake.buildDirectory": "${workspaceFolder}/build", "cmake.configureOnOpen": false, "C_Cpp.default.compilerPath": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "C_Cpp.default.compileCommands": "${workspaceFolder}/build/compile_commands.json", "C_Cpp.default.includePath": ["${workspaceFolder}/main", "${workspaceFolder}/components/**", "${workspaceFolder}/build/config", "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\components\\**"], "C_Cpp.default.browse.path": ["${workspaceFolder}", "C:\\Users\\<USER>\\Pictures\\ESP-IDF\\components"], "C_Cpp.default.defines": ["ESP_PLATFORM", "IDF_VER=\"v5.4.1\"", "CONFIG_IDF_TARGET_ESP32S3=1"], "files.associations": {"*.h": "c", "*.c": "c"}}