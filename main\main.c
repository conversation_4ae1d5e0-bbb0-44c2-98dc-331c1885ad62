#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <inttypes.h>
#include <time.h>
#include <math.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_vfs.h"
#include "esp_spiffs.h"
#include "driver/i2c.h"
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "nvs_flash.h"
#include "nvs.h"

#include "st7789.h"
#include "fontx.h"

#define INTERVAL 100
#define WAIT vTaskDelay(INTERVAL / portTICK_PERIOD_MS)

// Touch controller definitions
#define TOUCH_I2C_PORT I2C_NUM_0
#define TOUCH_SDA_PIN GPIO_NUM_11
#define TOUCH_SCL_PIN GPIO_NUM_10
#define TOUCH_RST_PIN GPIO_NUM_13
#define TOUCH_INT_PIN GPIO_NUM_14
#define CST816_I2C_ADDR 0x15

// Color sensor definitions (TCS3430)
#define COLOR_SENSOR_I2C_PORT I2C_NUM_1
#define COLOR_SENSOR_SDA_PIN GPIO_NUM_11  // Shared with touch
#define COLOR_SENSOR_SCL_PIN GPIO_NUM_10  // Shared with touch
#define TCS3430_I2C_ADDR 0x39

// Backlight PWM definitions
#define LEDC_TIMER              LEDC_TIMER_0
#define LEDC_MODE               LEDC_LOW_SPEED_MODE
#define LEDC_OUTPUT_IO          (15) // Define the output GPIO
#define LEDC_CHANNEL            LEDC_CHANNEL_0
#define LEDC_DUTY_RES           LEDC_TIMER_8_BIT // Set duty resolution to 8 bits
#define LEDC_DUTY               (128) // Set duty to 50%. ((2 ** 8) - 1) * 50% = 127
#define LEDC_FREQUENCY          (5000) // Frequency in Hertz. Set frequency at 5 kHz

static const char *TAG = "ColorMatching";

// Color definitions (if not already defined in st7789.h)
#ifndef ORANGE
#define ORANGE 0xFD20
#endif
#ifndef GRAY
#define GRAY 0x8410
#endif
#ifndef MAGENTA
#define MAGENTA 0xF81F
#endif

// CST816D Gesture IDs based on register map
typedef enum {
    GESTURE_NONE = 0x00,
    GESTURE_SWIPE_UP = 0x01,
    GESTURE_SWIPE_DOWN = 0x02,
    GESTURE_SWIPE_LEFT = 0x03,
    GESTURE_SWIPE_RIGHT = 0x04,
    GESTURE_SINGLE_CLICK = 0x05,
    GESTURE_DOUBLE_CLICK = 0x0B,
    GESTURE_LONG_PRESS = 0x0C
} cst816_gesture_t;

// Enhanced touch data structure with gesture support
typedef struct {
    uint16_t x;
    uint16_t y;
    bool pressed;
    uint8_t finger_num;
    cst816_gesture_t gesture;
    uint32_t timestamp;
} touch_data_t;

// Color sensor data structure
typedef struct {
    // Raw sensor data
    uint16_t raw_X, raw_Y, raw_Z;

    // Processed CIE 1931 XYZ tristimulus values
    float X, Y, Z;

    // CIE 1931 chromaticity coordinates
    float x, y;
    float Y_luminance;

    // RGB and Hex representation
    uint8_t r, g, b;
    char hex[8];

    // Color matching results
    char closestName[32];
    float deltaE;
    bool isVividWhite;

    // Timestamp
    uint32_t timestamp;
} color_data_t;

// Application state
typedef enum {
    STATE_LIVE_READING,
    STATE_CALIBRATING,
    STATE_MENU,
    STATE_SETTINGS,
    STATE_SETTINGS_DISPLAY,
    STATE_SETTINGS_SENSOR,
    STATE_TOUCH_CALIBRATION,
    STATE_ABOUT,
    STATE_ERROR_NO_DEVICE
} app_state_t;

// UI element structure for touch detection
typedef struct {
    uint16_t x;
    uint16_t y;
    uint16_t width;
    uint16_t height;
    app_state_t target_state;
    const char* label;
} ui_element_t;

// Touch calibration structure - Enhanced 6-point calibration
typedef struct {
    bool is_calibrated;
    bool swap_xy;
    int16_t map_x1, map_x2;
    int16_t map_y1, map_y2;
    // Enhanced calibration with 6 points for better accuracy
    uint16_t raw_points[6][2];    // Raw touch coordinates for 6 calibration points
    uint16_t screen_points[6][2]; // Corresponding screen coordinates
    // Additional calibration parameters for center region accuracy
    float center_x_scale;         // X scaling factor for center region
    float center_y_scale;         // Y scaling factor for center region
    int16_t center_x_offset;      // X offset for center region
    int16_t center_y_offset;      // Y offset for center region
    uint32_t magic_number;        // For validation (0xCAFEBABE)
} touch_calibration_t;

// Enhanced Touch Calibration State - User-Friendly Sequential Calibration
typedef struct {
    uint8_t current_point;        // Current calibration point (0-5 for 6 points)
    bool waiting_for_touch;       // Waiting for user to touch
    bool point_captured;          // Point has been captured
    bool point_touched;           // Flag for successful point touch
    bool show_feedback;           // Flag for visual feedback display
    bool show_retry_prompt;       // Show retry instruction for missed touches
    uint32_t last_touch_time;     // For debouncing
    uint32_t feedback_start_time; // Timestamp for feedback duration
    uint32_t retry_prompt_time;   // Timestamp for retry prompt display
    uint16_t target_radius;       // Large touch target radius (30px for 60px diameter)
    uint16_t acceptance_radius;   // Forgiving acceptance radius (40px for 80px diameter)
    uint16_t visual_radius;       // Visual target display radius (25-30px)
    touch_calibration_t cal_data; // Calibration data being collected
    bool calibration_complete;    // All points captured
    bool target_hit;              // Visual feedback for successful target hit
    uint32_t target_hit_time;     // Timestamp for target hit animation
} touch_cal_state_t;

// Settings structure
typedef struct {
    uint8_t brightness;
    bool auto_sleep;
    uint16_t auto_sleep_minutes;
    uint8_t sensor_integration;  // 0=2.78ms, 1=27.8ms, 2=101ms, 3=175ms, 4=712ms
    uint8_t sensor_gain;         // 0=1x, 1=4x, 2=16x, 3=64x
} settings_t;

// Global variables
static bool touch_initialized = false;
static bool color_sensor_initialized = false;
static app_state_t current_state = STATE_MENU; // Start with menu by default
static app_state_t previous_error_state = STATE_LIVE_READING; // Track where we came from for error screen
static color_data_t current_color_data = {0};
static settings_t app_settings = {
    .brightness = 100,
    .auto_sleep = false,
    .auto_sleep_minutes = 5,
    .sensor_integration = 1,  // 27.8ms default
    .sensor_gain = 0          // 1x default
};

// Touch calibration globals
static touch_calibration_t touch_calibration = {0};
static touch_cal_state_t touch_cal_state = {0};
static bool nvs_initialized = false;

// UI elements for current screen
static ui_element_t ui_elements[10];
static uint8_t ui_element_count = 0;

// Function prototypes
esp_err_t init_backlight_pwm(void);
void set_display_brightness(uint8_t brightness);
esp_err_t init_touch_controller(void);
bool read_touch_data(touch_data_t *touch_data);
bool read_raw_touch_data(touch_data_t *touch_data);
bool read_touch_data_for_calibration(touch_data_t *touch_data);
esp_err_t init_color_sensor(void);
bool read_color_sensor(color_data_t *color_data);
void process_color_data(color_data_t *data);
void handle_gesture(cst816_gesture_t gesture, app_state_t *current_state);
const char* gesture_to_string(cst816_gesture_t gesture);

// Touch calibration function prototypes
esp_err_t init_nvs(void);
esp_err_t load_touch_calibration(void);
esp_err_t save_touch_calibration(void);
void apply_touch_calibration(uint16_t raw_x, uint16_t raw_y, uint16_t *cal_x, uint16_t *cal_y);
void apply_manual_calibration_matrix(uint16_t raw_x, uint16_t raw_y, uint16_t *cal_x, uint16_t *cal_y);
void init_touch_calibration_state(void);
void calculate_calibration_parameters(void);
int16_t map_coordinate(int16_t value, int16_t from_low, int16_t from_high, int16_t to_low, int16_t to_high);
bool validate_touch_boundaries(uint16_t x, uint16_t y, uint16_t tolerance);
bool is_touch_within_button(uint16_t touch_x, uint16_t touch_y, uint16_t btn_x, uint16_t btn_y,
                           uint16_t btn_width, uint16_t btn_height, uint16_t tolerance);
bool is_touch_within_calibration_target(uint16_t touch_x, uint16_t touch_y, uint16_t target_x, uint16_t target_y, uint16_t radius);
void draw_large_calibration_target(TFT_t *dev, uint16_t x, uint16_t y, uint16_t color, bool hit_feedback);
void draw_calibration_progress(TFT_t *dev, FontxFile *font, uint8_t current_point, uint8_t total_points);
void show_calibration_feedback(TFT_t *dev, FontxFile *font, bool success, const char* message);
void safe_draw_string(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y, uint8_t *text, uint16_t color);
void safe_copy_and_draw_string(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y,
                               const char* source_text, uint16_t color, uint8_t* buffer, size_t buffer_size);
void ultra_safe_draw_string(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y,
                           const char* text, uint16_t color, size_t max_chars);

// UI helper functions
void clear_ui_elements(void);
void add_ui_element(uint16_t x, uint16_t y, uint16_t width, uint16_t height, app_state_t target_state, const char* label);
app_state_t check_touch_ui_elements(uint16_t touch_x, uint16_t touch_y);
void draw_button(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y, uint16_t width, uint16_t height, const char* text, uint16_t bg_color, uint16_t text_color);
void draw_panel(TFT_t *dev, uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint16_t bg_color);
void draw_title_bar(TFT_t *dev, FontxFile *font, const char* title, uint16_t bg_color);
void update_brightness_setting(void);

static void SPIFFS_Directory(char *path)
{
	DIR *dir = opendir(path);
	assert(dir != NULL);
	while (true)
	{
		struct dirent *pe = readdir(dir);
		if (!pe)
			break;
		ESP_LOGI(__FUNCTION__, "d_name=%s d_ino=%d d_type=%x", pe->d_name, pe->d_ino, pe->d_type);
	}
	closedir(dir);
}

// Initialize PWM for backlight control
esp_err_t init_backlight_pwm(void)
{
    // Prepare and then apply the LEDC PWM timer configuration
    ledc_timer_config_t ledc_timer = {
        .speed_mode       = LEDC_MODE,
        .timer_num        = LEDC_TIMER,
        .duty_resolution  = LEDC_DUTY_RES,
        .freq_hz          = LEDC_FREQUENCY,
        .clk_cfg          = LEDC_AUTO_CLK
    };
    ESP_ERROR_CHECK(ledc_timer_config(&ledc_timer));

    // Prepare and then apply the LEDC PWM channel configuration
    ledc_channel_config_t ledc_channel = {
        .speed_mode     = LEDC_MODE,
        .channel        = LEDC_CHANNEL,
        .timer_sel      = LEDC_TIMER,
        .intr_type      = LEDC_INTR_DISABLE,
        .gpio_num       = LEDC_OUTPUT_IO,
        .duty           = 0, // Set duty to 0%
        .hpoint         = 0
    };
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel));

    return ESP_OK;
}

// Set display brightness (0-255)
void set_display_brightness(uint8_t brightness)
{
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, LEDC_CHANNEL, brightness));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, LEDC_CHANNEL));
}

// Initialize NVS (Non-Volatile Storage)
esp_err_t init_nvs(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }

    if (ret == ESP_OK) {
        nvs_initialized = true;
        ESP_LOGI(TAG, "NVS initialized successfully");
    } else {
        ESP_LOGE(TAG, "Failed to initialize NVS: %s", esp_err_to_name(ret));
    }

    return ret;
}

// Load touch calibration data from NVS
esp_err_t load_touch_calibration(void)
{
    if (!nvs_initialized) {
        ESP_LOGW(TAG, "NVS not initialized, using default touch calibration");
        return ESP_FAIL;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("touch_cal", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGW(TAG, "No touch calibration data found, using defaults");
        return ESP_FAIL;
    }

    size_t required_size = sizeof(touch_calibration_t);
    err = nvs_get_blob(nvs_handle, "cal_data", &touch_calibration, &required_size);
    nvs_close(nvs_handle);

    if (err == ESP_OK && touch_calibration.magic_number == 0xCAFEBABE) {
        ESP_LOGI(TAG, "Touch calibration loaded successfully");
        ESP_LOGI(TAG, "Calibration: swap_xy=%d, map_x1=%d, map_x2=%d, map_y1=%d, map_y2=%d",
                touch_calibration.swap_xy, touch_calibration.map_x1, touch_calibration.map_x2,
                touch_calibration.map_y1, touch_calibration.map_y2);
        return ESP_OK;
    } else {
        ESP_LOGW(TAG, "Invalid touch calibration data, using defaults");
        return ESP_FAIL;
    }
}

// Save touch calibration data to NVS
esp_err_t save_touch_calibration(void)
{
    if (!nvs_initialized) {
        ESP_LOGE(TAG, "NVS not initialized, cannot save calibration");
        return ESP_FAIL;
    }

    // Set magic number for validation
    touch_calibration.magic_number = 0xCAFEBABE;

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("touch_cal", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS for writing: %s", esp_err_to_name(err));
        return err;
    }

    err = nvs_set_blob(nvs_handle, "cal_data", &touch_calibration, sizeof(touch_calibration_t));
    if (err == ESP_OK) {
        err = nvs_commit(nvs_handle);
        if (err == ESP_OK) {
            ESP_LOGI(TAG, "Touch calibration saved successfully");
        } else {
            ESP_LOGE(TAG, "Failed to commit calibration data: %s", esp_err_to_name(err));
        }
    } else {
        ESP_LOGE(TAG, "Failed to save calibration data: %s", esp_err_to_name(err));
    }

    nvs_close(nvs_handle);
    return err;
}

// Initialize I2C and touch controller
esp_err_t init_touch_controller(void)
{
    esp_err_t ret = ESP_OK;

    // Configure I2C with CST816D specifications
    // CST816D supports 10KHz~400KHz, using 400KHz for optimal performance
    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = TOUCH_SDA_PIN,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_io_num = TOUCH_SCL_PIN,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = 400000,  // Maximum supported by CST816D
    };

    ret = i2c_param_config(TOUCH_I2C_PORT, &conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C param config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = i2c_driver_install(TOUCH_I2C_PORT, conf.mode, 0, 0, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C driver install failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // Configure touch reset pin
    gpio_config_t rst_config = {
        .pin_bit_mask = (1ULL << TOUCH_RST_PIN),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    gpio_config(&rst_config);

    // CST816D reset sequence (per datasheet timing requirements)
    gpio_set_level(TOUCH_RST_PIN, 0);
    vTaskDelay(pdMS_TO_TICKS(20));  // Hold reset for 20ms minimum
    gpio_set_level(TOUCH_RST_PIN, 1);
    vTaskDelay(pdMS_TO_TICKS(100)); // Wait 100ms for initialization

    // Read CST816D identification registers for verification
    uint8_t chip_id = 0;
    uint8_t proj_id = 0;
    uint8_t fw_version = 0;

    // Read ChipID (register 0xA7) - Confirm CST816D detection
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xA7, true); // ChipID register
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, &chip_id, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read CST816D ChipID: %s", esp_err_to_name(ret));
        return ret;
    }

    // Read ProjID (register 0xA8) - Project identification
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xA8, true); // ProjID register
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, &proj_id, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    // Read FwVersion (register 0xA9) - Firmware version for debugging
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xA9, true); // FwVersion register
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, &fw_version, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    // Configure CST816D registers for optimal touch detection and gesture recognition
    // Based on CST816D register map specifications

    // Set touch threshold for improved sensitivity and accuracy
    uint8_t touch_threshold = 0x40; // Moderate sensitivity (range: 0x01-0xFF)
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x80, true); // Touch threshold register
    i2c_master_write_byte(cmd, touch_threshold, true);
    i2c_master_stop(cmd);
    i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    // Configure MotionMask register (0xEC) for gesture detection
    // Bit 0: Enable continuous left/right swipe
    // Bit 1: Enable continuous up/down swipe
    // Bit 2: Enable double click
    // Bit 3: Enable single click
    // Bit 4: Enable long press
    uint8_t motion_mask = 0x1F; // Enable all gestures (bits 0-4)
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xEC, true); // MotionMask register
    i2c_master_write_byte(cmd, motion_mask, true);
    i2c_master_stop(cmd);
    i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    // Set IrqPulseWidth register (0xED) for interrupt timing
    uint8_t irq_pulse_width = 0x0A; // 10 = 1ms pulse width (default recommended)
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xED, true); // IrqPulseWidth register
    i2c_master_write_byte(cmd, irq_pulse_width, true);
    i2c_master_stop(cmd);
    i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    // Configure AutoSleepTime register (0xF9) for power management
    uint8_t auto_sleep_time = 0x02; // 2 seconds auto sleep (default)
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xF9, true); // AutoSleepTime register
    i2c_master_write_byte(cmd, auto_sleep_time, true);
    i2c_master_stop(cmd);
    i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    // Set LpScanTH register (0xF5) for low-power wake sensitivity
    uint8_t lp_scan_th = 0x30; // 48 (0x30) default low-power scan threshold
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0xF5, true); // LpScanTH register
    i2c_master_write_byte(cmd, lp_scan_th, true);
    i2c_master_stop(cmd);
    i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    ESP_LOGI(TAG, "=== CST816D Touch Controller Initialized ===");
    ESP_LOGI(TAG, "ChipID: 0x%02X, ProjID: 0x%02X, FwVersion: 0x%02X",
             chip_id, proj_id, fw_version);
    ESP_LOGI(TAG, "Touch Threshold: 0x%02X, Motion Mask: 0x%02X (gestures enabled)",
             touch_threshold, motion_mask);
    ESP_LOGI(TAG, "IRQ Pulse Width: 0x%02X (1ms), Auto Sleep: 0x%02X (2s)",
             irq_pulse_width, auto_sleep_time);
    ESP_LOGI(TAG, "LP Scan Threshold: 0x%02X (wake sensitivity)", lp_scan_th);
    ESP_LOGI(TAG, "Gesture support: Single click, Double click, Long press, Swipes");

    touch_initialized = true;
    return ESP_OK;
}

// Map coordinate using linear interpolation
int16_t map_coordinate(int16_t value, int16_t from_low, int16_t from_high, int16_t to_low, int16_t to_high)
{
    return (value - from_low) * (to_high - to_low) / (from_high - from_low) + to_low;
}

// Validate touch coordinates are within acceptable display boundaries
// Accounts for ST7789V2 display area and CST816 active touch region
bool validate_touch_boundaries(uint16_t x, uint16_t y, uint16_t tolerance)
{
    // ST7789V2 display: 240×280 pixels
    // CST816 active area: may be smaller than visible display
    // Add tolerance for edge touch detection issues

    int16_t min_x = -(int16_t)tolerance;
    int16_t max_x = 240 + tolerance;
    int16_t min_y = -(int16_t)tolerance;
    int16_t max_y = 280 + tolerance;

    return (x >= min_x && x <= max_x && y >= min_y && y <= max_y);
}

// Enhanced touch-to-button collision detection with tolerance margins
// Addresses systematic Y-coordinate offset patterns and edge detection issues
bool is_touch_within_button(uint16_t touch_x, uint16_t touch_y, uint16_t btn_x, uint16_t btn_y,
                           uint16_t btn_width, uint16_t btn_height, uint16_t tolerance)
{
    // Apply Y-offset correction for display positioning
    uint16_t corrected_touch_y = touch_y;
    if (touch_y >= 20) {
        corrected_touch_y = touch_y - 20; // Subtract display Y-offset
    }

    // Expand button boundaries by tolerance to handle touch calibration inaccuracies
    int16_t left = btn_x - tolerance;
    int16_t right = btn_x + btn_width + tolerance;
    int16_t top = btn_y - tolerance;
    int16_t bottom = btn_y + btn_height + tolerance;

    // Ensure boundaries don't go negative or exceed display bounds
    if (left < 0) left = 0;
    if (top < 0) top = 0;
    if (right > 240) right = 240;
    if (bottom > 280) bottom = 280;

    // Use inclusive boundary detection (≤ instead of <) for edge cases
    bool x_within = (touch_x >= left && touch_x <= right);
    bool y_within = (corrected_touch_y >= top && corrected_touch_y <= bottom);

    return (x_within && y_within);
}

// Enhanced calibration target collision detection with forgiving acceptance radius
bool is_touch_within_calibration_target(uint16_t touch_x, uint16_t touch_y, uint16_t target_x, uint16_t target_y, uint16_t radius)
{
    // Apply Y-offset correction for display positioning
    uint16_t corrected_touch_y = touch_y;
    if (touch_y >= 20) {
        corrected_touch_y = touch_y - 20; // Subtract display Y-offset
    }

    // Calculate distance from touch point to target center
    int16_t dx = (int16_t)touch_x - (int16_t)target_x;
    int16_t dy = (int16_t)corrected_touch_y - (int16_t)target_y;

    // Use squared distance to avoid floating point sqrt calculation
    uint32_t distance_squared = (dx * dx) + (dy * dy);
    uint32_t radius_squared = radius * radius;

    return (distance_squared <= radius_squared);
}

// Enhanced touch target collision detection with debug logging
bool is_touch_within_target(uint16_t touch_x, uint16_t touch_y, uint16_t target_x, uint16_t target_y, uint16_t radius)
{
    // Apply Y-offset correction for display positioning
    uint16_t corrected_touch_y = touch_y;
    if (touch_y >= 20) {
        corrected_touch_y = touch_y - 20; // Subtract display Y-offset
    }

    // Calculate distance from touch point to target center
    int16_t dx = (int16_t)touch_x - (int16_t)target_x;
    int16_t dy = (int16_t)corrected_touch_y - (int16_t)target_y;

    // Use squared distance to avoid floating point sqrt calculation
    uint32_t distance_squared = (dx * dx) + (dy * dy);
    uint32_t radius_squared = radius * radius;

    // Debug logging with correct format specifiers for uint32_t
    ESP_LOGI(TAG, "Touch within target check: touch(%d,%d) target(%d,%d) radius=%d, distance_squared=%lu, radius_squared=%lu, hit=%s",
             touch_x, touch_y, target_x, target_y, radius,
             (unsigned long)distance_squared, (unsigned long)radius_squared,
             (distance_squared <= radius_squared) ? "YES" : "NO");

    return (distance_squared <= radius_squared);
}

// Draw large, highly visible calibration target with enhanced visual feedback
void draw_large_calibration_target(TFT_t *dev, uint16_t x, uint16_t y, uint16_t color, bool hit_feedback)
{
    if (hit_feedback) {
        // Success feedback: bright green with checkmark effect
        uint16_t success_color = GREEN;

        // Large filled circle for immediate visual confirmation
        lcdDrawFillCircle(dev, x, y, 35, success_color);

        // Bright center dot
        lcdDrawFillCircle(dev, x, y, 8, WHITE);

        // Simple checkmark lines
        lcdDrawLine(dev, x - 10, y, x - 5, y + 5, WHITE);
        lcdDrawLine(dev, x - 5, y + 5, x + 10, y - 8, WHITE);
    } else {
        // Normal target: large, clear, and easy to see

        // Outer ring (60px diameter = 30px radius)
        lcdDrawCircle(dev, x, y, 30, color);
        lcdDrawCircle(dev, x, y, 29, color); // Double line for visibility

        // Middle ring (40px diameter = 20px radius)
        lcdDrawCircle(dev, x, y, 20, color);

        // Inner ring (20px diameter = 10px radius)
        lcdDrawCircle(dev, x, y, 10, color);

        // Center filled circle (12px diameter = 6px radius)
        lcdDrawFillCircle(dev, x, y, 6, color);

        // Crosshair for precise targeting
        // Horizontal line
        lcdDrawLine(dev, x - 25, y, x - 12, y, color);
        lcdDrawLine(dev, x + 12, y, x + 25, y, color);
        // Vertical line
        lcdDrawLine(dev, x, y - 25, x, y - 12, color);
        lcdDrawLine(dev, x, y + 12, x, y + 25, color);

        // Corner markers for additional visual guidance
        // Top-left
        lcdDrawLine(dev, x - 35, y - 35, x - 30, y - 35, color);
        lcdDrawLine(dev, x - 35, y - 35, x - 35, y - 30, color);
        // Top-right
        lcdDrawLine(dev, x + 30, y - 35, x + 35, y - 35, color);
        lcdDrawLine(dev, x + 35, y - 35, x + 35, y - 30, color);
        // Bottom-left
        lcdDrawLine(dev, x - 35, y + 30, x - 35, y + 35, color);
        lcdDrawLine(dev, x - 35, y + 35, x - 30, y + 35, color);
        // Bottom-right
        lcdDrawLine(dev, x + 35, y + 30, x + 35, y + 35, color);
        lcdDrawLine(dev, x + 30, y + 35, x + 35, y + 35, color);
    }
}

// Draw clear progress indicator for sequential calibration
void draw_calibration_progress(TFT_t *dev, FontxFile *font, uint8_t current_point, uint8_t total_points)
{
    uint8_t text[64];
    uint16_t color;

    // Large, prominent progress indicator
    color = YELLOW;
    sprintf((char *)text, "Point %d of %d", current_point + 1, total_points);
    lcdDrawString(dev, font, 20, 50, text, color);

    // Progress bar visualization
    uint16_t bar_x = 20;
    uint16_t bar_y = 60;
    uint16_t bar_width = 200;
    uint16_t bar_height = 8;

    // Background bar
    lcdDrawRect(dev, bar_x, bar_y, bar_x + bar_width, bar_y + bar_height, WHITE);

    // Progress fill
    uint16_t progress_width = (bar_width * (current_point + 1)) / total_points;
    if (progress_width > 0) {
        lcdDrawFillRect(dev, bar_x + 1, bar_y + 1, bar_x + progress_width - 1, bar_y + bar_height - 1, GREEN);
    }
}

// Show calibration feedback messages with clear visual indication
void show_calibration_feedback(TFT_t *dev, FontxFile *font, bool success, const char* message)
{
    uint8_t text[128];
    uint16_t color = success ? GREEN : YELLOW;

    // Clear message area
    lcdDrawFillRect(dev, 10, 80, 230, 120, BLACK);

    // Display message with appropriate color
    strncpy((char *)text, message, sizeof(text) - 1);
    text[sizeof(text) - 1] = '\0';
    lcdDrawString(dev, font, 20, 100, text, color);
}

// ST7789V2 Datasheet-Optimized Touch Calibration System
// Based on ST7789V2 specifications: center-origin coordinate system,
// alignment marks at Y=251.58, chip dimensions 15155μm × 6984μm
void apply_touch_calibration(uint16_t raw_x, uint16_t raw_y, uint16_t *cal_x, uint16_t *cal_y)
{
    // CST816 touch controller analysis:
    // - Active touch area: typically 0-320 (X) × 0-240 (Y) raw coordinates
    // - Physical sensor area may be smaller than visible display
    // - Edge touches exhibit systematic Y-coordinate clamping

    // Check if enhanced calibration is available
    if (touch_cal_state.calibration_complete && touch_cal_state.cal_data.is_calibrated) {
        // Use enhanced 6-point calibration with ST7789V2 datasheet compliance
        int16_t x, y;

        if (touch_cal_state.cal_data.swap_xy) {
            x = map_coordinate(raw_y, touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, 0, 239);
            y = map_coordinate(raw_x, touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, 0, 279);
        } else {
            x = map_coordinate(raw_x, touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, 0, 239);
            y = map_coordinate(raw_y, touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, 0, 279);
        }

        // Enhanced center region correction with position-dependent compensation
        if (x > 60 && x < 180 && y > 70 && y < 210) {
            x += touch_cal_state.cal_data.center_x_offset;
            y += touch_cal_state.cal_data.center_y_offset;
        }

        // ST7789V2 alignment mark compensation (Y=251.58 datasheet specification)
        // Position-dependent Y-offset for systematic coordinate clamping issues
        int16_t y_offset = 20; // Base offset from alignment marks

        // Compensate for edge touch clamping (observed Y:0-1 top, Y:259 bottom)
        if (raw_y <= 5) {
            y_offset += 5; // Additional compensation for top edge clamping
        } else if (raw_y >= 235) {
            y_offset -= 5; // Reduce compensation for bottom edge clamping
        }

        y = y - y_offset;

        // ST7789V2-compliant bounds checking with proper display area
        *cal_x = (x < 0) ? 0 : ((x >= 240) ? 239 : x);
        *cal_y = (y < 0) ? 0 : ((y >= 280) ? 279 : y);

        ESP_LOGD(TAG, "Enhanced ST7789V2 calibration: raw(%d,%d) -> mapped(%d,%d) -> offset(%d) -> final(%d,%d)",
                 raw_x, raw_y, x + y_offset, y + y_offset, y_offset, *cal_x, *cal_y);
    } else {
        // ST7789V2 Datasheet-Optimized Fallback Calibration
        // Improved coordinate transformation based on physical chip specifications

        // CST816 to ST7789V2 coordinate system transformation
        // Account for center-origin vs top-left origin difference
        float x_scale = 240.0f / 320.0f;  // Direct linear scaling for X-axis
        float y_scale = 280.0f / 240.0f;  // Optimized Y-scaling for ST7789V2 280px height

        int16_t x = (int16_t)(raw_x * x_scale);
        int16_t y = (int16_t)(raw_y * y_scale);

        // ST7789V2 physical alignment compensation
        // Based on datasheet alignment marks at Y=251.58 (asymmetric positioning)
        int16_t y_offset = 20; // Base alignment mark offset

        // Position-dependent compensation for CST816 active area limitations
        // Address systematic Y-coordinate clamping at edges
        if (raw_y <= 5) {
            // Top edge: compensate for Y:0-1 clamping
            y_offset += (6 - raw_y); // Progressive compensation
        } else if (raw_y >= 235) {
            // Bottom edge: compensate for Y:259 clamping
            y_offset -= ((raw_y - 234) / 2); // Gradual reduction
        }

        y = y - y_offset;

        // ST7789V2 datasheet-compliant bounds with full 240×280 area
        *cal_x = (x < 0) ? 0 : ((x >= 240) ? 239 : x);
        *cal_y = (y < 0) ? 0 : ((y >= 280) ? 279 : y);

        ESP_LOGD(TAG, "ST7789V2 optimized calibration: raw(%d,%d) -> scaled(%.1f,%.1f) -> offset(%d) -> final(%d,%d)",
                 raw_x, raw_y, raw_x * x_scale, raw_y * y_scale, y_offset, *cal_x, *cal_y);
    }
}

// Manual calibration matrix transformation function
// This will be enhanced once manual calibration data is provided
void apply_manual_calibration_matrix(uint16_t raw_x, uint16_t raw_y, uint16_t *cal_x, uint16_t *cal_y)
{
    // PLACEHOLDER: This function will be implemented with proper transformation matrix
    // once manual calibration data is collected and analyzed

    // For now, use a simple linear transformation as a starting point
    // These values will be replaced with actual calibration matrix coefficients

    // Example transformation matrix (to be replaced with real data):
    // [cal_x]   [a  b  c] [raw_x]
    // [cal_y] = [d  e  f] [raw_y]
    // [ 1  ]    [0  0  1] [ 1  ]

    // Placeholder coefficients (will be calculated from manual calibration data)
    float a = 0.75f;  // X scaling factor
    float b = 0.0f;   // X-Y cross term
    float c = 0.0f;   // X offset
    float d = 0.0f;   // Y-X cross term
    float e = 1.17f;  // Y scaling factor
    float f = -20.0f; // Y offset

    // Apply transformation matrix
    float transformed_x = a * raw_x + b * raw_y + c;
    float transformed_y = d * raw_x + e * raw_y + f;

    // Clamp to display bounds
    *cal_x = (transformed_x < 0) ? 0 : ((transformed_x >= 240) ? 239 : (uint16_t)transformed_x);
    *cal_y = (transformed_y < 0) ? 0 : ((transformed_y >= 280) ? 279 : (uint16_t)transformed_y);

    ESP_LOGD(TAG, "Manual calibration: raw(%d,%d) -> matrix(%.1f,%.1f) -> final(%d,%d)",
             raw_x, raw_y, transformed_x, transformed_y, *cal_x, *cal_y);
}

// Enhanced CST816D touch data reading with gesture detection
// Based on CST816D register map specifications
bool read_raw_touch_data(touch_data_t *touch_data)
{
    if (!touch_initialized || !touch_data) {
        return false;
    }

    uint8_t data[8];  // Extended to read gesture data
    esp_err_t ret;

    // CST816D Enhanced Register Map:
    // 0x01: GestureID - Gesture detection result
    // 0x02: FingerNum - Number of touch points (bits 3:0)
    // 0x03: Touch1 X coordinate high byte (bits 3:0 valid)
    // 0x04: Touch1 X coordinate low byte
    // 0x05: Touch1 Y coordinate high byte (bits 3:0 valid)
    // 0x06: Touch1 Y coordinate low byte
    // 0x07: Touch1 pressure/area (optional)

    // Read enhanced touch data from CST816D starting at register 0x01
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x01, true); // GestureID register
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read(cmd, data, 7, I2C_MASTER_LAST_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        ESP_LOGD(TAG, "CST816D I2C read failed: %s", esp_err_to_name(ret));
        return false;
    }

    // Parse CST816D enhanced touch data
    uint8_t gesture_id = data[0];           // GestureID register (0x01)
    uint8_t finger_num = data[1] & 0x0F;    // FingerNum register (0x02), bits 3:0

    // Initialize touch data structure
    touch_data->gesture = (cst816_gesture_t)gesture_id;
    touch_data->finger_num = finger_num;
    touch_data->timestamp = xTaskGetTickCount() * portTICK_PERIOD_MS;

    // Check for valid touch data
    if (finger_num > 0 && finger_num <= 1) {  // CST816D supports single touch
        // Extract 12-bit coordinates (datasheet specifies 12-bit resolution)
        uint16_t raw_x = ((data[2] & 0x0F) << 8) | data[3];  // X: bits 11:0
        uint16_t raw_y = ((data[4] & 0x0F) << 8) | data[5];  // Y: bits 11:0

        // Validate coordinate ranges (CST816D typical range: 0-4095 for 12-bit)
        if (raw_x <= 4095 && raw_y <= 4095) {
            touch_data->x = raw_x;
            touch_data->y = raw_y;
            touch_data->pressed = true;

            ESP_LOGD(TAG, "CST816D touch: gesture=0x%02X, fingers=%d, X=%d, Y=%d",
                     gesture_id, finger_num, raw_x, raw_y);
            return true;
        } else {
            ESP_LOGW(TAG, "CST816D invalid coordinates: X=%d, Y=%d", raw_x, raw_y);
        }
    }

    // Check for gesture-only events (no coordinate data)
    if (gesture_id != GESTURE_NONE) {
        touch_data->pressed = false;
        touch_data->x = 0;
        touch_data->y = 0;
        ESP_LOGI(TAG, "CST816D gesture detected: 0x%02X", gesture_id);
        return true;
    }

    // No valid touch or gesture detected
    touch_data->pressed = false;
    touch_data->gesture = GESTURE_NONE;
    touch_data->finger_num = 0;
    touch_data->x = 0;
    touch_data->y = 0;
    return false;
}

// Enhanced touch data reading with gesture detection and calibration
bool read_touch_data(touch_data_t *touch_data)
{
    if (!touch_initialized || !touch_data) {
        return false;
    }

    uint8_t data[8];  // Extended for gesture data
    esp_err_t ret;

    // Read enhanced touch data from CST816D
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x01, true); // GestureID register
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read(cmd, data, 7, I2C_MASTER_LAST_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        return false;
    }

    // Parse enhanced touch data with gesture support
    uint8_t gesture_id = data[0];           // GestureID register
    uint8_t finger_num = data[1] & 0x0F;    // FingerNum register

    // Set gesture and metadata
    touch_data->gesture = (cst816_gesture_t)gesture_id;
    touch_data->finger_num = finger_num;
    touch_data->timestamp = xTaskGetTickCount() * portTICK_PERIOD_MS;

    // Process coordinate data if touch is detected
    if (finger_num > 0) {
        // Read raw coordinates
        uint16_t raw_x = ((data[2] & 0x0F) << 8) | data[3];
        uint16_t raw_y = ((data[4] & 0x0F) << 8) | data[5];

        // Apply coordinate transformation using calibration data
        apply_touch_calibration(raw_x, raw_y, &touch_data->x, &touch_data->y);

        touch_data->pressed = true;

        // Clean raw coordinate logging for calibration data collection
        // Only log raw coordinates when touch is detected (no spam)
        ESP_LOGI(TAG, "Raw: X:%d, Y:%d", raw_x, raw_y);

        return true;
    } else if (gesture_id != GESTURE_NONE) {
        // Gesture-only event (no coordinate data)
        touch_data->pressed = false;
        touch_data->x = 0;
        touch_data->y = 0;
        ESP_LOGI(TAG, "Gesture-only event: 0x%02X", gesture_id);
        return true;
    } else {
        // No touch or gesture
        touch_data->pressed = false;
        touch_data->gesture = GESTURE_NONE;
        return false;
    }
}

// Special function for manual calibration data collection
// Logs only raw coordinates without transformation for mapping analysis
bool read_touch_data_for_calibration(touch_data_t *touch_data)
{
    if (!touch_initialized || !touch_data) {
        return false;
    }

    uint8_t data[8];  // Extended for gesture data
    esp_err_t ret;

    // Read enhanced touch data from CST816D
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x01, true); // GestureID register
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CST816_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read(cmd, data, 7, I2C_MASTER_LAST_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        return false;
    }

    // Parse enhanced touch data with gesture support
    uint8_t gesture_id = data[0];           // GestureID register
    uint8_t finger_num = data[1] & 0x0F;    // FingerNum register

    // Set gesture and metadata
    touch_data->gesture = (cst816_gesture_t)gesture_id;
    touch_data->finger_num = finger_num;
    touch_data->timestamp = xTaskGetTickCount() * portTICK_PERIOD_MS;

    // Process coordinate data if touch is detected
    if (finger_num > 0) {
        // Read raw coordinates (NO CALIBRATION APPLIED)
        uint16_t raw_x = ((data[2] & 0x0F) << 8) | data[3];
        uint16_t raw_y = ((data[4] & 0x0F) << 8) | data[5];

        // Store raw coordinates directly (no transformation)
        touch_data->x = raw_x;
        touch_data->y = raw_y;
        touch_data->pressed = true;

        // Clean logging for manual calibration data collection
        ESP_LOGI(TAG, "Raw: X:%d, Y:%d", raw_x, raw_y);

        return true;
    } else {
        // No touch detected
        touch_data->pressed = false;
        touch_data->gesture = GESTURE_NONE;
        return false;
    }
}

// Initialize TCS3430 color sensor
esp_err_t init_color_sensor(void)
{
    esp_err_t ret = ESP_OK;

    // TCS3430 uses the same I2C bus as touch controller
    // I2C is already initialized in init_touch_controller()

    // Try to detect the sensor first
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCS3430_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(50));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        ESP_LOGD(TAG, "TCS3430 sensor not detected at address 0x%02X", TCS3430_I2C_ADDR);
        return ESP_FAIL;
    }

    // Enable the sensor
    uint8_t enable_cmd = 0x01; // PON (Power ON)
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCS3430_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x80, true); // ENABLE register
    i2c_master_write_byte(cmd, enable_cmd, true);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        ESP_LOGD(TAG, "Failed to enable TCS3430 sensor");
        return ret;
    }

    vTaskDelay(pdMS_TO_TICKS(10)); // Wait for power on

    // Enable AEN (ALS Enable)
    enable_cmd = 0x03; // PON + AEN
    cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCS3430_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x80, true); // ENABLE register
    i2c_master_write_byte(cmd, enable_cmd, true);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        ESP_LOGD(TAG, "Failed to enable ALS on TCS3430");
        return ret;
    }

    ESP_LOGI(TAG, "TCS3430 color sensor initialized successfully");

    return ESP_OK;
}

// Read color sensor data from TCS3430
bool read_color_sensor(color_data_t *color_data)
{
    if (!color_sensor_initialized || !color_data) {
        return false;
    }

    esp_err_t ret;
    uint8_t data[8];

    // Read X, Y, Z data from TCS3430 (6 bytes total, 2 bytes each)
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCS3430_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, 0x94, true); // X data low register (auto-increment)
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCS3430_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read(cmd, data, 6, I2C_MASTER_LAST_NACK);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(TOUCH_I2C_PORT, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);

    if (ret != ESP_OK) {
        return false;
    }

    // Parse raw sensor data (little endian)
    color_data->raw_X = (data[1] << 8) | data[0];
    color_data->raw_Y = (data[3] << 8) | data[2];
    color_data->raw_Z = (data[5] << 8) | data[4];

    // Set timestamp
    color_data->timestamp = xTaskGetTickCount() * portTICK_PERIOD_MS;

    return true;
}

// Process color data - convert raw values to meaningful color information
void process_color_data(color_data_t *data)
{
    if (!data) return;

    // Convert raw values to CIE XYZ (simplified conversion)
    // These would normally require calibration matrices
    data->X = (float)data->raw_X / 1000.0f;
    data->Y = (float)data->raw_Y / 1000.0f;
    data->Z = (float)data->raw_Z / 1000.0f;

    // Calculate chromaticity coordinates
    float sum = data->X + data->Y + data->Z;
    if (sum > 0) {
        data->x = data->X / sum;
        data->y = data->Y / sum;
    } else {
        data->x = 0.0f;
        data->y = 0.0f;
    }
    data->Y_luminance = data->Y;

    // Convert XYZ to RGB (simplified sRGB conversion)
    float r_linear = 3.2406f * data->X - 1.5372f * data->Y - 0.4986f * data->Z;
    float g_linear = -0.9689f * data->X + 1.8758f * data->Y + 0.0415f * data->Z;
    float b_linear = 0.0557f * data->X - 0.2040f * data->Y + 1.0570f * data->Z;

    // Clamp to valid range
    r_linear = fmaxf(0.0f, fminf(1.0f, r_linear));
    g_linear = fmaxf(0.0f, fminf(1.0f, g_linear));
    b_linear = fmaxf(0.0f, fminf(1.0f, b_linear));

    // Convert to 8-bit RGB
    data->r = (uint8_t)(r_linear * 255.0f);
    data->g = (uint8_t)(g_linear * 255.0f);
    data->b = (uint8_t)(b_linear * 255.0f);

    // Generate hex string
    snprintf(data->hex, sizeof(data->hex), "#%02X%02X%02X", data->r, data->g, data->b);

    // Simple color name detection (basic implementation)
    if (data->r > 200 && data->g > 200 && data->b > 200) {
        strcpy(data->closestName, "White");
        data->isVividWhite = true;
    } else if (data->r < 50 && data->g < 50 && data->b < 50) {
        strcpy(data->closestName, "Black");
        data->isVividWhite = false;
    } else if (data->r > data->g && data->r > data->b) {
        strcpy(data->closestName, "Red");
        data->isVividWhite = false;
    } else if (data->g > data->r && data->g > data->b) {
        strcpy(data->closestName, "Green");
        data->isVividWhite = false;
    } else if (data->b > data->r && data->b > data->g) {
        strcpy(data->closestName, "Blue");
        data->isVividWhite = false;
    } else {
        strcpy(data->closestName, "Mixed");
        data->isVividWhite = false;
    }

    // Calculate simple delta E (placeholder)
    data->deltaE = sqrtf(powf(data->X - 0.95047f, 2) + powf(data->Y - 1.0f, 2) + powf(data->Z - 1.08883f, 2));
}

// Convert gesture ID to human-readable string for debugging
const char* gesture_to_string(cst816_gesture_t gesture)
{
    switch (gesture) {
        case GESTURE_NONE: return "None";
        case GESTURE_SWIPE_UP: return "Swipe Up";
        case GESTURE_SWIPE_DOWN: return "Swipe Down";
        case GESTURE_SWIPE_LEFT: return "Swipe Left";
        case GESTURE_SWIPE_RIGHT: return "Swipe Right";
        case GESTURE_SINGLE_CLICK: return "Single Click";
        case GESTURE_DOUBLE_CLICK: return "Double Click";
        case GESTURE_LONG_PRESS: return "Long Press";
        default: return "Unknown";
    }
}

// Handle gesture events for enhanced user interaction
void handle_gesture(cst816_gesture_t gesture, app_state_t *current_state)
{
    if (gesture == GESTURE_NONE) {
        return;
    }

    ESP_LOGI(TAG, "Gesture detected: %s (0x%02X)", gesture_to_string(gesture), gesture);

    switch (gesture) {
        case GESTURE_SINGLE_CLICK:
            // Standard touch interaction - handled by normal touch processing
            ESP_LOGD(TAG, "Single click - normal touch processing");
            break;

        case GESTURE_DOUBLE_CLICK:
            // Quick menu access or special functions
            ESP_LOGI(TAG, "Double click - Quick menu access");
            if (*current_state == STATE_MENU) {
                *current_state = STATE_SETTINGS;
                ESP_LOGI(TAG, "Double click: Switching to Settings");
            } else if (*current_state == STATE_LIVE_READING) {
                *current_state = STATE_MENU;
                ESP_LOGI(TAG, "Double click: Returning to Main Menu");
            }
            break;

        case GESTURE_LONG_PRESS:
            // Context menu or calibration mode entry
            ESP_LOGI(TAG, "Long press - Entering calibration mode");
            *current_state = STATE_TOUCH_CALIBRATION;
            break;

        case GESTURE_SWIPE_UP:
            // Navigate up or increase values
            ESP_LOGI(TAG, "Swipe up - Navigate up/increase");
            if (*current_state == STATE_SETTINGS) {
                // Could adjust brightness or other settings
                ESP_LOGD(TAG, "Swipe up in settings - increase brightness");
            }
            break;

        case GESTURE_SWIPE_DOWN:
            // Navigate down or decrease values
            ESP_LOGI(TAG, "Swipe down - Navigate down/decrease");
            if (*current_state == STATE_SETTINGS) {
                // Could adjust brightness or other settings
                ESP_LOGD(TAG, "Swipe down in settings - decrease brightness");
            }
            break;

        case GESTURE_SWIPE_LEFT:
            // Navigate to previous screen or option
            ESP_LOGI(TAG, "Swipe left - Previous/Back");
            if (*current_state == STATE_LIVE_READING) {
                *current_state = STATE_MENU;
                ESP_LOGI(TAG, "Swipe left: Returning to Main Menu");
            } else if (*current_state == STATE_SETTINGS) {
                *current_state = STATE_MENU;
                ESP_LOGI(TAG, "Swipe left: Returning to Main Menu from Settings");
            }
            break;

        case GESTURE_SWIPE_RIGHT:
            // Navigate to next screen or option
            ESP_LOGI(TAG, "Swipe right - Next/Forward");
            if (*current_state == STATE_MENU) {
                *current_state = STATE_LIVE_READING;
                ESP_LOGI(TAG, "Swipe right: Entering Live Reading");
            }
            break;

        default:
            ESP_LOGW(TAG, "Unknown gesture: 0x%02X", gesture);
            break;
    }
}

// Initialize Enhanced User-Friendly Touch Calibration State
void init_touch_calibration_state(void)
{
    memset(&touch_cal_state, 0, sizeof(touch_cal_state_t));
    touch_cal_state.waiting_for_touch = true;
    touch_cal_state.current_point = 0;
    touch_cal_state.point_captured = false;
    touch_cal_state.show_feedback = false;
    touch_cal_state.show_retry_prompt = false;
    touch_cal_state.target_hit = false;

    // Enhanced target sizing for better user experience and forgiving detection
    touch_cal_state.target_radius = 45;        // Large visual target radius (90px diameter)
    touch_cal_state.acceptance_radius = 50;    // Very forgiving acceptance radius (100px diameter)
    touch_cal_state.visual_radius = 40;        // Core visual element radius

    // Initialize calibration data
    memset(&touch_cal_state.cal_data, 0, sizeof(touch_calibration_t));

    // ST7789V2-Optimized 6-Point Calibration Layout
    // Based on CST816 active area analysis and ST7789V2 datasheet specifications
    // Positioned with larger margins to avoid edge touch detection issues

    // Top-left corner - positioned well within reliable CST816 active area
    touch_cal_state.cal_data.screen_points[0][0] = 50;  // 50px margin from left edge
    touch_cal_state.cal_data.screen_points[0][1] = 60;  // 60px margin from top edge

    // Top-right corner - account for CST816 active area limitations with larger margins
    touch_cal_state.cal_data.screen_points[1][0] = 190; // 50px margin from right edge (240-50)
    touch_cal_state.cal_data.screen_points[1][1] = 60;  // 60px margin from top edge

    // Bottom-right corner - positioned to avoid Y-coordinate clamping with larger margins
    touch_cal_state.cal_data.screen_points[2][0] = 190; // 50px margin from right edge
    touch_cal_state.cal_data.screen_points[2][1] = 220; // 60px margin from bottom (280-60)

    // Bottom-left corner - symmetric positioning for consistent mapping with larger margins
    touch_cal_state.cal_data.screen_points[3][0] = 50;  // 50px margin from left edge
    touch_cal_state.cal_data.screen_points[3][1] = 220; // 60px margin from bottom

    // Center-top - optimized for ST7789V2 center-origin coordinate system
    touch_cal_state.cal_data.screen_points[4][0] = 120; // Exact center X (240/2)
    touch_cal_state.cal_data.screen_points[4][1] = 110; // Upper center region (moved down)

    // Center-bottom - balanced positioning for enhanced center accuracy
    touch_cal_state.cal_data.screen_points[5][0] = 120; // Exact center X (240/2)
    touch_cal_state.cal_data.screen_points[5][1] = 170; // Lower center region (moved up)

    ESP_LOGI(TAG, "Touch calibration state initialized with 6 points");
}

// ST7789V2 Datasheet-Compliant Calibration Parameter Calculation
// Enhanced algorithm accounting for center-origin coordinate system and alignment marks
void calculate_calibration_parameters(void)
{
    // Use corner points for basic calibration (points 0-3)
    uint16_t *tl_raw = touch_cal_state.cal_data.raw_points[0];    // Top-left raw
    uint16_t *br_raw = touch_cal_state.cal_data.raw_points[2];    // Bottom-right raw
    uint16_t *tl_screen = touch_cal_state.cal_data.screen_points[0]; // Top-left screen
    uint16_t *br_screen = touch_cal_state.cal_data.screen_points[2]; // Bottom-right screen

    // ST7789V2 coordinate system analysis
    // Check if we should swap X and Y based on the touch pattern
    int16_t raw_width = abs(br_raw[0] - tl_raw[0]);
    int16_t raw_height = abs(br_raw[1] - tl_raw[1]);
    int16_t screen_width = abs(br_screen[0] - tl_screen[0]);
    int16_t screen_height = abs(br_screen[1] - tl_screen[1]);

    // CST816 vs ST7789V2 orientation detection
    // Account for potential coordinate system mismatch
    if ((raw_width > raw_height) != (screen_width > screen_height)) {
        touch_cal_state.cal_data.swap_xy = true;
        ESP_LOGI(TAG, "ST7789V2 coordinate swap detected (center-origin compensation)");
    } else {
        touch_cal_state.cal_data.swap_xy = false;
        ESP_LOGI(TAG, "ST7789V2 coordinate systems aligned");
    }

    // Calculate basic mapping parameters using corner points
    if (touch_cal_state.cal_data.swap_xy) {
        // Swap raw coordinates for mapping calculation
        touch_cal_state.cal_data.map_x1 = tl_raw[1];
        touch_cal_state.cal_data.map_x2 = br_raw[1];
        touch_cal_state.cal_data.map_y1 = tl_raw[0];
        touch_cal_state.cal_data.map_y2 = br_raw[0];
    } else {
        touch_cal_state.cal_data.map_x1 = tl_raw[0];
        touch_cal_state.cal_data.map_x2 = br_raw[0];
        touch_cal_state.cal_data.map_y1 = tl_raw[1];
        touch_cal_state.cal_data.map_y2 = br_raw[1];
    }

    // Enhanced calibration: Calculate center region correction using points 4 and 5
    uint16_t *center1_raw = touch_cal_state.cal_data.raw_points[4];    // Center-top raw
    uint16_t *center2_raw = touch_cal_state.cal_data.raw_points[5];    // Center-bottom raw
    uint16_t *center1_screen = touch_cal_state.cal_data.screen_points[4]; // Center-top screen
    uint16_t *center2_screen = touch_cal_state.cal_data.screen_points[5]; // Center-bottom screen

    // Calculate expected center coordinates using basic linear mapping
    int16_t expected_center1_x, expected_center1_y, expected_center2_x, expected_center2_y;

    if (touch_cal_state.cal_data.swap_xy) {
        expected_center1_x = map_coordinate(center1_raw[1], touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, 0, 239); // Full 240px width
        expected_center1_y = map_coordinate(center1_raw[0], touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, 0, 259); // Effective 260px height (280-20)
        expected_center2_x = map_coordinate(center2_raw[1], touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, 0, 239); // Full 240px width
        expected_center2_y = map_coordinate(center2_raw[0], touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, 0, 259); // Effective 260px height (280-20)
    } else {
        expected_center1_x = map_coordinate(center1_raw[0], touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, 0, 239); // Full 240px width
        expected_center1_y = map_coordinate(center1_raw[1], touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, 0, 259); // Effective 260px height (280-20)
        expected_center2_x = map_coordinate(center2_raw[0], touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, 0, 239); // Full 240px width
        expected_center2_y = map_coordinate(center2_raw[1], touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, 0, 259); // Effective 260px height (280-20)
    }

    // Calculate center region correction factors
    float center_error_x1 = (float)(center1_screen[0] - expected_center1_x);
    float center_error_y1 = (float)(center1_screen[1] - expected_center1_y);
    float center_error_x2 = (float)(center2_screen[0] - expected_center2_x);
    float center_error_y2 = (float)(center2_screen[1] - expected_center2_y);

    // Average the errors for center region correction
    touch_cal_state.cal_data.center_x_offset = (int16_t)((center_error_x1 + center_error_x2) / 2.0f);
    touch_cal_state.cal_data.center_y_offset = (int16_t)((center_error_y1 + center_error_y2) / 2.0f);

    // Calculate scaling factors for center region (default to 1.0 if no significant error)
    touch_cal_state.cal_data.center_x_scale = 1.0f;
    touch_cal_state.cal_data.center_y_scale = 1.0f;

    // Validate calibration quality and set calibrated flag
    // Check for reasonable mapping ranges to ensure calibration accuracy
    int16_t x_range = abs(touch_cal_state.cal_data.map_x2 - touch_cal_state.cal_data.map_x1);
    int16_t y_range = abs(touch_cal_state.cal_data.map_y2 - touch_cal_state.cal_data.map_y1);

    // ST7789V2 calibration validation criteria
    bool calibration_valid = true;
    if (x_range < 100 || x_range > 400) {
        ESP_LOGW(TAG, "X-axis calibration range suspicious: %d (expected 100-400)", x_range);
        calibration_valid = false;
    }
    if (y_range < 80 || y_range > 300) {
        ESP_LOGW(TAG, "Y-axis calibration range suspicious: %d (expected 80-300)", y_range);
        calibration_valid = false;
    }

    touch_cal_state.cal_data.is_calibrated = calibration_valid;

    ESP_LOGI(TAG, "ST7789V2-Optimized 6-point calibration %s:",
             calibration_valid ? "completed successfully" : "completed with warnings");
    ESP_LOGI(TAG, "  swap_xy: %d", touch_cal_state.cal_data.swap_xy);
    ESP_LOGI(TAG, "  map_x1: %d, map_x2: %d (range: %d)",
             touch_cal_state.cal_data.map_x1, touch_cal_state.cal_data.map_x2, x_range);
    ESP_LOGI(TAG, "  map_y1: %d, map_y2: %d (range: %d)",
             touch_cal_state.cal_data.map_y1, touch_cal_state.cal_data.map_y2, y_range);
    ESP_LOGI(TAG, "  center_x_offset: %d, center_y_offset: %d",
             touch_cal_state.cal_data.center_x_offset, touch_cal_state.cal_data.center_y_offset);
    ESP_LOGI(TAG, "  center_x_scale: %.3f, center_y_scale: %.3f",
             touch_cal_state.cal_data.center_x_scale, touch_cal_state.cal_data.center_y_scale);
    ESP_LOGI(TAG, "  calibration_quality: %s", calibration_valid ? "GOOD" : "NEEDS_IMPROVEMENT");
}

// Safe text drawing function that ensures text stays within display bounds and prevents buffer overflow
void safe_draw_string(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y, uint8_t *text, uint16_t color)
{
    // Get font height for boundary checking
    uint8_t font_height = getFortHeight(font);

    // Ensure X coordinate is within bounds (restored to full 240px width)
    if (x >= 240) {
        x = 239;
    }

    // Ensure Y coordinate allows text to fit within display height
    // Y coordinate represents the bottom of the text
    // Fixed for full 280-pixel height (ESP32-S3-Touch-LCD-1.69 native resolution)
    if (y > (280 - 5)) { // Leave 5 pixel margin from bottom
        y = 280 - 5;
    }

    // Ensure text doesn't start too high (though this is less likely)
    if (y < font_height) {
        y = font_height;
    }

    // Draw the text with safe coordinates
    lcdDrawString(dev, font, x, y, text, color);
}

// Enhanced safe text copy and draw function to prevent buffer overflow and text corruption
void safe_copy_and_draw_string(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y,
                               const char* source_text, uint16_t color, uint8_t* buffer, size_t buffer_size)
{
    // Clear the buffer completely to prevent garbage characters
    memset(buffer, 0, buffer_size);

    // Use safe string copy with explicit null termination
    strncpy((char*)buffer, source_text, buffer_size - 1);
    buffer[buffer_size - 1] = '\0';

    // Call the safe drawing function
    safe_draw_string(dev, font, x, y, buffer, color);
}

// Ultra-safe string drawing function that manually controls character rendering
void ultra_safe_draw_string(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y,
                           const char* text, uint16_t color, size_t max_chars)
{
    if (!text || !font || !dev) return;

    // Get font dimensions
    uint8_t font_width = getFortWidth(font);
    uint8_t font_height = getFortHeight(font);

    // Calculate safe boundaries using full 240px width
    uint16_t max_x = (240 > font_width) ? (240 - font_width) : 0;
    uint16_t safe_y = (y < font_height) ? font_height : y;
    if (safe_y > 280) safe_y = 280; // Fixed for full 280px height

    // Limit starting position
    if (x > max_x) x = max_x;

    // Draw characters one by one with strict bounds checking
    uint16_t current_x = x;
    size_t text_len = strlen(text);
    size_t chars_to_draw = (text_len < max_chars) ? text_len : max_chars;

    for (size_t i = 0; i < chars_to_draw; i++) {
        // Check if character is printable ASCII
        if (text[i] < 32 || text[i] > 126) {
            break; // Stop at first non-printable character
        }

        // Check if we have enough space for this character
        if (current_x + font_width > 240) {
            break; // Stop if character would go off screen
        }

        // Draw single character
        uint8_t single_char[2] = {(uint8_t)text[i], '\0'};
        current_x = lcdDrawChar(dev, font, current_x, safe_y, single_char[0], color);

        // Safety check - if lcdDrawChar returns invalid position, stop
        if (current_x >= 240 || current_x < x) {
            break;
        }
    }
}

// UI helper function implementations
void clear_ui_elements(void) {
    ui_element_count = 0;
    memset(ui_elements, 0, sizeof(ui_elements));
}

void add_ui_element(uint16_t x, uint16_t y, uint16_t width, uint16_t height, app_state_t target_state, const char* label) {
    if (ui_element_count < 10) {
        ui_elements[ui_element_count].x = x;
        ui_elements[ui_element_count].y = y;
        ui_elements[ui_element_count].width = width;
        ui_elements[ui_element_count].height = height;
        ui_elements[ui_element_count].target_state = target_state;
        ui_elements[ui_element_count].label = label;
        ui_element_count++;
    }
}

app_state_t check_touch_ui_elements(uint16_t touch_x, uint16_t touch_y) {
    // Debug: Log touch coordinates and all UI element bounds
    ESP_LOGI(TAG, "Touch at (%d,%d) - checking %d UI elements:", touch_x, touch_y, ui_element_count);

    // Apply Y-offset correction for display positioning
    uint16_t corrected_touch_y = touch_y;
    if (touch_y >= 20) {
        corrected_touch_y = touch_y - 20; // Subtract display Y-offset
    }

    for (uint8_t i = 0; i < ui_element_count; i++) {
        ESP_LOGI(TAG, "Element %d '%s': bounds (%d,%d) to (%d,%d)",
                i, ui_elements[i].label,
                ui_elements[i].x, ui_elements[i].y,
                ui_elements[i].x + ui_elements[i].width,
                ui_elements[i].y + ui_elements[i].height);

        // Enhanced touch detection with tolerance margins and inclusive boundary checking
        const uint16_t tolerance = 7; // 7px tolerance margin for better responsiveness

        // Expand button boundaries by tolerance to handle touch calibration inaccuracies
        int16_t left = ui_elements[i].x - tolerance;
        int16_t right = ui_elements[i].x + ui_elements[i].width + tolerance;
        int16_t top = ui_elements[i].y - tolerance;
        int16_t bottom = ui_elements[i].y + ui_elements[i].height + tolerance;

        // Ensure boundaries don't go negative or exceed display bounds
        if (left < 0) left = 0;
        if (top < 0) top = 0;
        if (right > 240) right = 240;
        if (bottom > 280) bottom = 280;

        // Use inclusive bounds checking (≤ instead of <) with tolerance margins
        if (touch_x >= left && touch_x <= right &&
            corrected_touch_y >= top && corrected_touch_y <= bottom) {
            ESP_LOGI(TAG, "*** UI element touched: %s (expanded bounds: %d,%d to %d,%d) ***",
                    ui_elements[i].label, left, top, right, bottom);
            return ui_elements[i].target_state;
        }
    }
    ESP_LOGI(TAG, "No UI element touched at (%d,%d) corrected Y: %d", touch_x, touch_y, corrected_touch_y);
    return current_state; // No element touched, stay in current state
}

void draw_button(TFT_t *dev, FontxFile *font, uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                 const char* text, uint16_t bg_color, uint16_t text_color) {
    // Create shadow effect WITHIN button boundaries to prevent overlap
    // Draw shadow as a subtle inset effect instead of extending beyond button
    uint16_t shadow_color = rgb565(15, 15, 15); // Darker shadow for inset effect

    // Draw shadow lines only within button boundaries (inset shadow)
    lcdDrawLine(dev, x + 1, y + height - 1, x + width - 1, y + height - 1, shadow_color);
    lcdDrawLine(dev, x + width - 1, y + 1, x + width - 1, y + height - 1, shadow_color);

    // Create gradient effect for button background
    for (int i = 0; i < height - 1; i++) { // Leave bottom line for shadow
        // Calculate gradient from lighter at top to darker at bottom
        uint8_t r = ((bg_color >> 11) & 0x1F) << 3;
        uint8_t g = ((bg_color >> 5) & 0x3F) << 2;
        uint8_t b = (bg_color & 0x1F) << 3;

        // Apply gradient (lighter at top, darker at bottom)
        float gradient_factor = 1.0f + (0.3f * (1.0f - (float)i / height));
        if (gradient_factor > 1.3f) gradient_factor = 1.3f;

        r = (uint8_t)(r * gradient_factor);
        g = (uint8_t)(g * gradient_factor);
        b = (uint8_t)(b * gradient_factor);

        if (r > 255) r = 255;
        if (g > 255) g = 255;
        if (b > 255) b = 255;

        uint16_t gradient_color = rgb565(r, g, b);
        lcdDrawLine(dev, x, y + i, x + width - 2, y + i, gradient_color); // Leave right edge for shadow
    }

    // Draw button border with highlight effect - all within button boundaries
    uint16_t highlight_color = rgb565(200, 200, 200); // Light highlight
    uint16_t border_color = rgb565(120, 120, 120);    // Medium border

    // Top and left highlight (raised effect)
    lcdDrawLine(dev, x, y, x + width - 1, y, highlight_color);
    lcdDrawLine(dev, x, y, x, y + height - 1, highlight_color);

    // Outer border for definition
    lcdDrawLine(dev, x, y, x + width - 1, y, border_color);
    lcdDrawLine(dev, x, y, x, y + height - 1, border_color);
    lcdDrawLine(dev, x, y + height - 1, x + width - 1, y + height - 1, border_color);
    lcdDrawLine(dev, x + width - 1, y, x + width - 1, y + height - 1, border_color);

    // Get font dimensions for proper centering
    uint8_t font_width = getFortWidth(font);
    uint8_t font_height = getFortHeight(font);

    // Center text in button - Y coordinate is the BOTTOM of the text
    uint16_t text_width = strlen(text) * font_width;
    uint16_t text_x = x + (width - text_width) / 2;
    uint16_t text_y = y + (height + font_height) / 2; // Bottom of text should be in center + half font height
    lcdDrawString(dev, font, text_x, text_y, (uint8_t*)text, text_color);
}

void draw_panel(TFT_t *dev, uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint16_t bg_color) {
    // Removed shadow effect to prevent overlap with bottom UI elements
    // uint16_t shadow_color = rgb565(15, 15, 15);
    // lcdDrawFillRect(dev, x + 3, y + 3, x + width + 3, y + height + 3, shadow_color);

    // Create subtle gradient for panel background
    for (int i = 0; i < height; i++) {
        uint8_t r = ((bg_color >> 11) & 0x1F) << 3;
        uint8_t g = ((bg_color >> 5) & 0x3F) << 2;
        uint8_t b = (bg_color & 0x1F) << 3;

        // Very subtle gradient
        float gradient_factor = 1.0f + (0.1f * (1.0f - (float)i / height));

        r = (uint8_t)(r * gradient_factor);
        g = (uint8_t)(g * gradient_factor);
        b = (uint8_t)(b * gradient_factor);

        if (r > 255) r = 255;
        if (g > 255) g = 255;
        if (b > 255) b = 255;

        uint16_t gradient_color = rgb565(r, g, b);
        lcdDrawLine(dev, x, y + i, x + width - 1, y + i, gradient_color);
    }

    // Draw border with inset effect
    uint16_t dark_border = rgb565(80, 80, 80);
    uint16_t light_border = rgb565(180, 180, 180);

    // Dark top and left (inset effect)
    lcdDrawLine(dev, x, y, x + width - 1, y, dark_border);
    lcdDrawLine(dev, x, y, x, y + height - 1, dark_border);

    // Light bottom and right
    lcdDrawLine(dev, x + 1, y + height - 1, x + width - 1, y + height - 1, light_border);
    lcdDrawLine(dev, x + width - 1, y + 1, x + width - 1, y + height - 1, light_border);
}

void draw_title_bar(TFT_t *dev, FontxFile *font, const char* title, uint16_t bg_color) {
    // Draw title bar with gradient (height: 45 pixels to ensure text fits)
    // Fixed to use full 240px width for ESP32-S3-Touch-LCD-1.69
    for (int i = 0; i < 45; i++) {
        uint8_t r = ((bg_color >> 11) & 0x1F) << 3;
        uint8_t g = ((bg_color >> 5) & 0x3F) << 2;
        uint8_t b = (bg_color & 0x1F) << 3;

        // Strong gradient for title bar
        float gradient_factor = 1.0f + (0.5f * (1.0f - (float)i / 45));

        r = (uint8_t)(r * gradient_factor);
        g = (uint8_t)(g * gradient_factor);
        b = (uint8_t)(b * gradient_factor);

        if (r > 255) r = 255;
        if (g > 255) g = 255;
        if (b > 255) b = 255;

        uint16_t gradient_color = rgb565(r, g, b);
        lcdDrawLine(dev, 0, i, 239, i, gradient_color); // Restored to 239 (240-1) for full width
    }

    // Draw title text centered with proper positioning and safe buffer handling
    uint8_t font_width = getFortWidth(font);
    uint8_t font_height = getFortHeight(font);

    // Create safe buffer for title text
    uint8_t safe_title[64];
    memset(safe_title, 0, sizeof(safe_title));
    strncpy((char*)safe_title, title, sizeof(safe_title) - 1);
    safe_title[sizeof(safe_title) - 1] = '\0';

    uint16_t text_width = strlen((char*)safe_title) * font_width;
    uint16_t text_x = (240 - text_width) / 2; // Restored to center in full 240px width

    // Position text in center of title bar (Y coordinate is bottom of text)
    // Title bar is 45 pixels high, center the text vertically
    uint16_t text_y = (45 + font_height) / 2;

    // Draw text with shadow effect using safe buffer
    lcdDrawString(dev, font, text_x + 1, text_y + 1, safe_title, BLACK);
    lcdDrawString(dev, font, text_x, text_y, safe_title, WHITE);
}

void update_brightness_setting(void) {
    // Apply brightness setting to display
    uint8_t pwm_value = (app_settings.brightness * 255) / 100;
    set_display_brightness(pwm_value);
    ESP_LOGI(TAG, "Brightness updated to %d%% (PWM: %d)", app_settings.brightness, pwm_value);
}

// Configuration values are set using menuconfig









// Main color matching application
void color_matching_app(void *pvParameters)
{
	ESP_LOGI(TAG, "Starting Professional Color Matching Tool");

	// Initialize font files
	FontxFile fx16G[2];
	FontxFile fx24G[2];
	InitFontx(fx16G, "/spiffs/ILGH16XB.FNT", ""); // 8x16Dot Gothic
	InitFontx(fx24G, "/spiffs/ILGH24XB.FNT", ""); // 12x24Dot Gothic

	TFT_t dev;

	// Initialize SPI and LCD with viewport adjustment
	ESP_LOGI(TAG, "Initializing SPI and ST7789 display...");
	spi_master_init(&dev, CONFIG_MOSI_GPIO, CONFIG_SCLK_GPIO, CONFIG_CS_GPIO, CONFIG_DC_GPIO, CONFIG_RESET_GPIO, CONFIG_BL_GPIO);

	// Use full display resolution for ESP32-S3-Touch-LCD-1.69
	int display_width = CONFIG_WIDTH;        // 240 pixels
	int display_height = CONFIG_HEIGHT;      // 280 pixels
	int display_offset_x = CONFIG_OFFSETX;   // 0
	int display_offset_y = CONFIG_OFFSETY + 20;   // Add 20px downward offset to fix upward shift

	ESP_LOGI(TAG, "Display viewport: %dx%d at offset (%d,%d)",
			display_width, display_height, display_offset_x, display_offset_y);
	ESP_LOGI(TAG, "Applied +20px Y-offset to fix vertical positioning issue");

	lcdInit(&dev, display_width, display_height, display_offset_x, display_offset_y);

#if CONFIG_INVERSION
	ESP_LOGI(TAG, "Enable Display Inversion");
	lcdInversionOff(&dev);
#endif

	// Initialize NVS for settings storage
	ESP_LOGI(TAG, "Initializing NVS...");
	ESP_ERROR_CHECK(init_nvs());

	// Initialize backlight PWM control
	ESP_LOGI(TAG, "Initializing backlight PWM...");
	ESP_ERROR_CHECK(init_backlight_pwm());
	set_display_brightness(255); // Full brightness

	// Initialize touch controller
	ESP_LOGI(TAG, "Initializing touch controller...");
	ESP_ERROR_CHECK(init_touch_controller());

	// Load touch calibration data automatically
	ESP_LOGI(TAG, "Loading touch calibration...");
	esp_err_t cal_result = load_touch_calibration();
	if (cal_result == ESP_OK) {
		ESP_LOGI(TAG, "Touch calibration loaded successfully");
	} else {
		ESP_LOGW(TAG, "No touch calibration found, using simple mapping");
		// Set default calibration (simple direct mapping)
		touch_calibration.is_calibrated = false;
		touch_calibration.swap_xy = false;
		touch_calibration.map_x1 = 0;
		touch_calibration.map_x2 = 240; // Full display width
		touch_calibration.map_y1 = 0;
		touch_calibration.map_y2 = 280; // Full display height
	}

	// Initialize color sensor (optional)
	ESP_LOGI(TAG, "Attempting to initialize color sensor...");
	esp_err_t color_sensor_ret = init_color_sensor();
	if (color_sensor_ret != ESP_OK) {
		ESP_LOGW(TAG, "Color sensor not found - running in demo mode");
		color_sensor_initialized = false;
	} else {
		ESP_LOGI(TAG, "Color sensor initialized successfully");
		color_sensor_initialized = true;
	}

	ESP_LOGI(TAG, "All systems initialized successfully! Resolution: %dx%d (viewport adjusted)", display_width, display_height);

	// Display startup screen with thorough buffer clearing
	ESP_LOGI(TAG, "Clearing frame buffer to eliminate any initialization artifacts...");
	lcdClearFrameBuffer(&dev, BLACK); // Explicitly clear frame buffer to prevent white noise
	lcdFillScreen(&dev, BLACK);
	lcdSetFontDirection(&dev, 0);

	// Additional buffer clearing to prevent text rendering artifacts
	vTaskDelay(pdMS_TO_TICKS(50)); // Allow display to settle

	// Title - Center "Color Matching" text properly in full 240px width
	uint16_t color = CYAN;
	uint8_t text[64];

	// Calculate centered position for title using original 240px width
	uint8_t font_width = getFortWidth(fx24G);
	const char* title_text = "Color Matching";
	uint16_t text_width = strlen(title_text) * font_width;
	uint16_t text_x = (240 - text_width) / 2; // Center in full 240px width

	// Use enhanced safe copy and draw function with proper centering
	safe_copy_and_draw_string(&dev, fx24G, text_x, 30, title_text, color, text, sizeof(text));

	// Status - Enhanced safe buffer handling
	color = GREEN;
	safe_copy_and_draw_string(&dev, fx16G, 30, 60, "Professional Tool", color, text, sizeof(text));

	// Hardware info - Enhanced safe buffer handling
	color = WHITE;
	safe_copy_and_draw_string(&dev, fx16G, 30, 90, "ESP32-S3 Display", color, text, sizeof(text));

	// Sensor status - Enhanced safe buffer handling
	if (color_sensor_initialized) {
		color = GREEN;
		safe_copy_and_draw_string(&dev, fx16G, 20, 110, "TCS3430 Sensor: OK", color, text, sizeof(text));
	} else {
		color = ORANGE;
		safe_copy_and_draw_string(&dev, fx16G, 20, 110, "TCS3430 Sensor: Not Found", color, text, sizeof(text));
	}

	// Mode indication - Enhanced safe buffer handling
	color = YELLOW;
	safe_copy_and_draw_string(&dev, fx16G, 50, 130, "Full Version", color, text, sizeof(text));

	lcdDrawFinish(&dev);
	vTaskDelay(pdMS_TO_TICKS(2000));

	// Main application loop
	touch_data_t touch_data;
	uint32_t last_update = 0;
	uint32_t last_display_update = 0;
	app_state_t previous_state = STATE_SETTINGS; // Force initial display update

	// Set initial state based on scanner availability
	if (color_sensor_initialized) {
		current_state = STATE_LIVE_READING; // Start with live reading if scanner available
	} else {
		current_state = STATE_MENU; // Start with menu if no scanner
	}

	ESP_LOGI(TAG, "=== Starting main application loop ===");
	const char* initial_state_names[] = {"LIVE_READING", "CALIBRATING", "MENU", "SETTINGS", "SETTINGS_DISPLAY", "SETTINGS_SENSOR", "TOUCH_CALIBRATION", "ABOUT", "ERROR_NO_DEVICE"};
	ESP_LOGI(TAG, "Initial state: %s", initial_state_names[current_state]);

	while (1) {
		uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;

		// Handle touch input and gesture detection for enhanced navigation
		if (read_touch_data(&touch_data)) {
			// Handle gesture events first (they can change state without coordinates)
			if (touch_data.gesture != GESTURE_NONE) {
				handle_gesture(touch_data.gesture, &current_state);
			}

			// Handle coordinate-based touch events
			if (touch_data.pressed) {
				// Removed verbose touch logging to reduce debug spam
				// Raw coordinates are logged in read_touch_data() function

				if (current_time - last_update > 300) { // Debounce
					app_state_t old_state = current_state;
					app_state_t new_state = current_state;

				if (current_state == STATE_ERROR_NO_DEVICE) {
					// Return from error screen to menu
					new_state = STATE_MENU;
					ESP_LOGI(TAG, "Returning from error screen to menu");
				} else if (current_state == STATE_TOUCH_CALIBRATION) {
					// Enhanced User-Friendly Touch Calibration with Forgiving Detection
					if (touch_cal_state.current_point < 6 && touch_cal_state.waiting_for_touch) {
						uint16_t target_x = touch_cal_state.cal_data.screen_points[touch_cal_state.current_point][0];
						uint16_t target_y = touch_cal_state.cal_data.screen_points[touch_cal_state.current_point][1];

						// Accept any touch with valid coordinates during calibration
						touch_cal_state.cal_data.raw_points[touch_cal_state.current_point][0] = touch_data.x;
						touch_cal_state.cal_data.raw_points[touch_cal_state.current_point][1] = touch_data.y;

						// Calculate offset from ideal target for automatic compensation
						int16_t offset_x = (int16_t)touch_data.x - (int16_t)target_x;
						int16_t offset_y = (int16_t)touch_data.y - (int16_t)target_y;

						ESP_LOGI(TAG, "Calibration point %d captured: touch(%d,%d) target(%d,%d) offset(%d,%d)",
								touch_cal_state.current_point,
								touch_data.x, touch_data.y,
								target_x, target_y,
								offset_x, offset_y);

						// Show immediate feedback message
						touch_cal_state.target_hit = true;
						touch_cal_state.target_hit_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
						touch_cal_state.point_captured = true;
						touch_cal_state.waiting_for_touch = false;
						touch_cal_state.show_retry_prompt = false;

						// Brief delay for visual feedback
						vTaskDelay(pdMS_TO_TICKS(500));

						// Advance to next point
						touch_cal_state.current_point++;

						// Reset state for next point or complete calibration
						if (touch_cal_state.current_point < 6) {
							// Prepare for next point
							touch_cal_state.waiting_for_touch = true;
							touch_cal_state.point_captured = false;
							touch_cal_state.target_hit = false;
							touch_cal_state.show_feedback = false;
						} else {
							// Calibration complete
							touch_cal_state.calibration_complete = true;

							// Calculate enhanced calibration parameters
							calculate_calibration_parameters();

							// Copy calibration data to global structure
							memcpy(&touch_calibration, &touch_cal_state.cal_data, sizeof(touch_calibration_t));

							// Save to NVS automatically
							esp_err_t save_result = save_touch_calibration();

							// Show completion message
							lcdFillScreen(&dev, BLACK);
							lcdSetFontDirection(&dev, 0);

							color = GREEN;
							strcpy((char *)text, "Calibration Complete!");
							lcdDrawString(&dev, fx24G, 20, 120, text, color);

							if (save_result == ESP_OK) {
								color = WHITE;
								strcpy((char *)text, "Touch accuracy improved");
								lcdDrawString(&dev, fx16G, 20, 150, text, color);
							} else {
								color = YELLOW;
								strcpy((char *)text, "Save failed - using temporary");
								lcdDrawString(&dev, fx16G, 10, 150, text, color);
							}

							lcdDrawFinish(&dev);
							vTaskDelay(pdMS_TO_TICKS(2500)); // Show message for 2.5 seconds

							// Return to settings menu
							new_state = STATE_SETTINGS;
							ESP_LOGI(TAG, "Enhanced touch calibration completed successfully");
						}
					} else {
						// Check for cancel button or calibration already complete
						new_state = check_touch_ui_elements(touch_data.x, touch_data.y);
					}
				} else {
					// Check if touch hit any UI elements
					new_state = check_touch_ui_elements(touch_data.x, touch_data.y);

					// Handle special button actions (settings adjustments) with enhanced touch detection
					// Apply Y-offset correction for display positioning
					uint16_t corrected_touch_y = touch_data.y;
					if (touch_data.y >= 20) {
						corrected_touch_y = touch_data.y - 20; // Subtract display Y-offset
					}

					for (uint8_t i = 0; i < ui_element_count; i++) {
						// Enhanced touch detection with tolerance margins and inclusive boundary checking
						const uint16_t tolerance = 7; // 7px tolerance margin for better responsiveness

						// Expand button boundaries by tolerance
						int16_t left = ui_elements[i].x - tolerance;
						int16_t right = ui_elements[i].x + ui_elements[i].width + tolerance;
						int16_t top = ui_elements[i].y - tolerance;
						int16_t bottom = ui_elements[i].y + ui_elements[i].height + tolerance;

						// Ensure boundaries don't go negative or exceed display bounds
						if (left < 0) left = 0;
						if (top < 0) top = 0;
						if (right > 240) right = 240;
						if (bottom > 280) bottom = 280;

						// Use inclusive bounds checking with tolerance margins
						if (touch_data.x >= left && touch_data.x <= right &&
							corrected_touch_y >= top && corrected_touch_y <= bottom) {

							// Handle brightness controls
							if (strcmp(ui_elements[i].label, "Brightness-") == 0) {
								if (app_settings.brightness > 10) {
									app_settings.brightness -= 10;
									update_brightness_setting();
									new_state = current_state; // Stay on same screen to show update
								}
							} else if (strcmp(ui_elements[i].label, "Brightness+") == 0) {
								if (app_settings.brightness < 100) {
									app_settings.brightness += 10;
									update_brightness_setting();
									new_state = current_state; // Stay on same screen to show update
								}
							}
							// Handle auto-sleep toggle
							else if (strcmp(ui_elements[i].label, "AutoSleep Toggle") == 0) {
								app_settings.auto_sleep = !app_settings.auto_sleep;
								new_state = current_state; // Stay on same screen to show update
							}
							// Handle sensor integration time
							else if (strcmp(ui_elements[i].label, "Integration-") == 0) {
								if (app_settings.sensor_integration > 0) {
									app_settings.sensor_integration--;
									new_state = current_state;
								}
							} else if (strcmp(ui_elements[i].label, "Integration+") == 0) {
								if (app_settings.sensor_integration < 4) {
									app_settings.sensor_integration++;
									new_state = current_state;
								}
							}
							// Handle sensor gain
							else if (strcmp(ui_elements[i].label, "Gain-") == 0) {
								if (app_settings.sensor_gain > 0) {
									app_settings.sensor_gain--;
									new_state = current_state;
								}
							} else if (strcmp(ui_elements[i].label, "Gain+") == 0) {
								if (app_settings.sensor_gain < 3) {
									app_settings.sensor_gain++;
									new_state = current_state;
								}
							}
							break;
						}
					}

					// Handle scanner requirement checking for scanner-dependent states
					if ((new_state == STATE_LIVE_READING || new_state == STATE_CALIBRATING) && !color_sensor_initialized) {
						previous_error_state = current_state;
						new_state = STATE_ERROR_NO_DEVICE;
					}
				}

				if (new_state != current_state) {
					current_state = new_state;
					last_update = current_time;

					const char* state_names[] = {"LIVE_READING", "CALIBRATING", "MENU", "SETTINGS", "SETTINGS_DISPLAY", "SETTINGS_SENSOR", "TOUCH_CALIBRATION", "ABOUT", "ERROR_NO_DEVICE"};
					ESP_LOGI(TAG, "State transition: %s -> %s", state_names[old_state], state_names[current_state]);
				}
			}
		}
	}

		// Update display based on current state (only when state changes or every 2 seconds)
		bool should_update = (current_state != previous_state) ||
							 (current_time - last_display_update > 2000);

		if (should_update) {
			last_display_update = current_time;
			previous_state = current_state;
		}

		switch (current_state) {
			case STATE_LIVE_READING:
				if (should_update) {
					lcdClearFrameBuffer(&dev, BLACK); // Clear frame buffer first
					lcdFillScreen(&dev, BLACK);
					lcdSetFontDirection(&dev, 0);

					// Clear UI elements and add a full-screen touch area for easy navigation
					clear_ui_elements();
					add_ui_element(0, 0, 240, 280, STATE_MENU, "Full Screen"); // Full display area
				}

				if (color_sensor_initialized) {
					// Read and display color sensor data
					if (read_color_sensor(&current_color_data)) {
						process_color_data(&current_color_data);

						// Title
						color = CYAN;
						strcpy((char *)text, "Live Color Reading");
						lcdDrawString(&dev, fx16G, 10, 10, text, color);

						// Color swatch
						uint16_t color_rgb = rgb565(current_color_data.r, current_color_data.g, current_color_data.b);
						lcdDrawFillRect(&dev, 10, 30, 110, 80, color_rgb);
						lcdDrawRect(&dev, 10, 30, 110, 80, WHITE);

						// RGB values
						color = WHITE;
						sprintf((char *)text, "R:%d G:%d B:%d", current_color_data.r, current_color_data.g, current_color_data.b);
						lcdDrawString(&dev, fx16G, 120, 40, text, color);

						// Hex value
						sprintf((char *)text, "Hex: %s", current_color_data.hex);
						lcdDrawString(&dev, fx16G, 120, 60, text, color);

						// XYZ values
						sprintf((char *)text, "X:%.2f Y:%.2f Z:%.2f", current_color_data.X, current_color_data.Y, current_color_data.Z);
						lcdDrawString(&dev, fx16G, 10, 100, text, color);

						// Chromaticity
						sprintf((char *)text, "x:%.3f y:%.3f", current_color_data.x, current_color_data.y);
						lcdDrawString(&dev, fx16G, 10, 120, text, color);

						// Color name
						sprintf((char *)text, "Color: %s", current_color_data.closestName);
						lcdDrawString(&dev, fx16G, 10, 140, text, GREEN);

						// Delta E
						sprintf((char *)text, "Delta E: %.1f", current_color_data.deltaE);
						lcdDrawString(&dev, fx16G, 10, 160, text, YELLOW);
					} else {
						// Sensor read failed
						color = RED;
						strcpy((char *)text, "Sensor Read Error");
						lcdDrawString(&dev, fx16G, 10, 10, text, color);
					}
				} else {
					// Full version - no sensor connected
					color = CYAN;
					strcpy((char *)text, "Live Color Reading");
					lcdDrawString(&dev, fx16G, 10, 10, text, color);

					color = WHITE;
					strcpy((char *)text, "Ready to scan colors");
					lcdDrawString(&dev, fx16G, 10, 50, text, color);
					strcpy((char *)text, "Place object under sensor");
					lcdDrawString(&dev, fx16G, 10, 70, text, color);

					// Placeholder for color swatch
					lcdDrawRect(&dev, 10, 100, 110, 150, WHITE);
					color = GRAY;
					strcpy((char *)text, "Color");
					lcdDrawString(&dev, fx16G, 40, 120, text, color);
					strcpy((char *)text, "Preview");
					lcdDrawString(&dev, fx16G, 30, 135, text, color);

					// Measurement info
					color = WHITE;
					strcpy((char *)text, "RGB: --- --- ---");
					lcdDrawString(&dev, fx16G, 120, 100, text, color);
					strcpy((char *)text, "Hex: ------");
					lcdDrawString(&dev, fx16G, 120, 120, text, color);
					strcpy((char *)text, "XYZ: --- --- ---");
					lcdDrawString(&dev, fx16G, 10, 170, text, color);
					strcpy((char *)text, "Color: Not detected");
					lcdDrawString(&dev, fx16G, 10, 190, text, color);
				}

				// Instructions
				color = GRAY;
				strcpy((char *)text, "Touch to change mode");
				lcdDrawString(&dev, fx16G, 10, 230, text, color);

				// Sensor status notification at bottom - Fixed for full 280px height
				if (!color_sensor_initialized) {
					color = ORANGE;
					strcpy((char *)text, "Scanning device not found");
					lcdDrawString(&dev, fx16G, 10, 270, text, color); // Moved down to use full height
				}

				lcdDrawFinish(&dev);
				break;

			case STATE_CALIBRATING:
				// Display calibration screen
				lcdFillScreen(&dev, BLACK);
				lcdSetFontDirection(&dev, 0);

				color = YELLOW;
				strcpy((char *)text, "Calibration Mode");
				lcdDrawString(&dev, fx24G, 20, 50, text, color);

				color = WHITE;
				strcpy((char *)text, "Place white reference");
				lcdDrawString(&dev, fx16G, 10, 100, text, color);
				strcpy((char *)text, "under sensor");
				lcdDrawString(&dev, fx16G, 10, 120, text, color);

				color = GREEN;
				strcpy((char *)text, "Calibrating...");
				lcdDrawString(&dev, fx16G, 10, 160, text, color);

				color = GRAY;
				strcpy((char *)text, "Touch to continue");
				lcdDrawString(&dev, fx16G, 10, 250, text, color);

				lcdDrawFinish(&dev);
				break;

			case STATE_MENU:
				if (should_update) {
					// Clear UI elements and setup new ones for menu
					clear_ui_elements();

					// LAYER 1: Background elements (drawn first, lowest z-index)
					lcdClearFrameBuffer(&dev, rgb565(20, 20, 40)); // Clear frame buffer first
					lcdFillScreen(&dev, rgb565(20, 20, 40));
					lcdSetFontDirection(&dev, 0);

					// LAYER 2: Title bar (background element)
					draw_title_bar(&dev, fx24G, "Color Matching", CYAN);

					// LAYER 3: Content panel (background element) - Keep 220px width, fix Y positioning
					draw_panel(&dev, 10, 55, 220, 200, rgb565(40, 40, 60));

					// LAYER 4: Interactive buttons (drawn last, highest z-index)
					uint16_t button_color;
					uint16_t start_y = 65; // Start below title bar with margin

					// Live Reading button
					button_color = color_sensor_initialized ? rgb565(0, 100, 200) : rgb565(80, 80, 80);
					draw_button(&dev, fx16G, 20, start_y, 200, 32, "Live Reading", button_color, WHITE);
					add_ui_element(20, start_y, 200, 32, STATE_LIVE_READING, "Live Reading");

					// Calibration button
					button_color = color_sensor_initialized ? rgb565(0, 100, 200) : rgb565(80, 80, 80);
					draw_button(&dev, fx16G, 20, start_y + 40, 200, 32, "Calibration", button_color, WHITE);
					add_ui_element(20, start_y + 40, 200, 32, STATE_CALIBRATING, "Calibration");

					// Settings button
					draw_button(&dev, fx16G, 20, start_y + 80, 200, 32, "Settings", rgb565(0, 150, 0), WHITE);
					add_ui_element(20, start_y + 80, 200, 32, STATE_SETTINGS, "Settings");

					// About button
					draw_button(&dev, fx16G, 20, start_y + 120, 200, 32, "About", rgb565(200, 100, 0), WHITE);
					add_ui_element(20, start_y + 120, 200, 32, STATE_ABOUT, "About");

					// LAYER 5: Overlay text and indicators (drawn after buttons to ensure visibility)
					if (!color_sensor_initialized) {
						// Draw status indicators next to disabled buttons
						color = RED;
						strcpy((char *)text, "*");
						lcdDrawString(&dev, fx16G, 5, start_y + 20, text, color);
						lcdDrawString(&dev, fx16G, 5, start_y + 60, text, color);

						// Legend at bottom
						strcpy((char *)text, "* Requires color scanner");
						lcdDrawString(&dev, fx16G, 20, 270, text, color);
					}

					lcdDrawFinish(&dev);
				}
				break;

			case STATE_SETTINGS:
				if (should_update) {
					clear_ui_elements();

					// LAYER 1: Background elements
					lcdFillScreen(&dev, rgb565(20, 20, 40));
					lcdSetFontDirection(&dev, 0);

					// LAYER 2: Title bar
					draw_title_bar(&dev, fx24G, "Settings", CYAN);

					// LAYER 3: Content panel - Keep 220px width, fix Y positioning
					draw_panel(&dev, 10, 55, 220, 185, rgb565(40, 40, 60));

					// LAYER 4: Information text (before buttons to avoid overlap)
					uint16_t start_y = 70; // Start below title bar
					color = WHITE;
					sprintf((char *)text, "Brightness: %d%%", app_settings.brightness);
					lcdDrawString(&dev, fx16G, 30, start_y + 125, text, color);

					strcpy((char *)text, app_settings.auto_sleep ? "Auto-sleep: On" : "Auto-sleep: Off");
					lcdDrawString(&dev, fx16G, 30, start_y + 145, text, color);

					// LAYER 5: Interactive buttons (highest z-index)
					// Display Settings button
					draw_button(&dev, fx16G, 20, start_y, 200, 32, "Display Settings", rgb565(0, 100, 200), WHITE);
					add_ui_element(20, start_y, 200, 32, STATE_SETTINGS_DISPLAY, "Display Settings");

					// Sensor Settings button
					uint16_t sensor_color = color_sensor_initialized ? rgb565(0, 100, 200) : rgb565(80, 80, 80);
					draw_button(&dev, fx16G, 20, start_y + 40, 200, 32, "Sensor Settings", sensor_color, WHITE);
					add_ui_element(20, start_y + 40, 200, 32, STATE_SETTINGS_SENSOR, "Sensor Settings");

					// Touch Calibration button
					draw_button(&dev, fx16G, 20, start_y + 80, 200, 32, "Touch Calibration", rgb565(150, 0, 150), WHITE);
					add_ui_element(20, start_y + 80, 200, 32, STATE_TOUCH_CALIBRATION, "Touch Calibration");

					// Back button
					draw_button(&dev, fx16G, 20, 240, 80, 30, "Back", rgb565(200, 0, 0), WHITE);
					add_ui_element(20, 240, 80, 30, STATE_MENU, "Back");

					// LAYER 6: Overlay indicators (drawn last to ensure visibility)
					if (!color_sensor_initialized) {
						color = RED;
						strcpy((char *)text, "*");
						lcdDrawString(&dev, fx16G, 5, start_y + 60, text, color);
					}

					lcdDrawFinish(&dev);
				}
				break;

			case STATE_SETTINGS_DISPLAY:
				if (should_update) {
					clear_ui_elements();

					// LAYER 1: Background
					lcdFillScreen(&dev, BLACK);
					lcdSetFontDirection(&dev, 0);

					// LAYER 2: Title and information text
					color = CYAN;
					strcpy((char *)text, "Display Settings");
					lcdDrawString(&dev, fx24G, 30, 30, text, color);

					// Brightness control
					color = WHITE;
					sprintf((char *)text, "Brightness: %d%%", app_settings.brightness);
					lcdDrawString(&dev, fx16G, 20, 80, text, color);

					// Auto-sleep setting
					sprintf((char *)text, "Auto-sleep: %s", app_settings.auto_sleep ? "On" : "Off");
					lcdDrawString(&dev, fx16G, 20, 150, text, color);

					if (app_settings.auto_sleep) {
						sprintf((char *)text, "Sleep after: %d min", app_settings.auto_sleep_minutes);
						lcdDrawString(&dev, fx16G, 20, 210, text, color);
					}

					// LAYER 3: Interactive buttons (highest z-index)
					// Brightness adjustment buttons
					draw_button(&dev, fx16G, 20, 100, 40, 25, "-", RED, WHITE);
					add_ui_element(20, 100, 40, 25, current_state, "Brightness-");

					draw_button(&dev, fx16G, 180, 100, 40, 25, "+", GREEN, WHITE);
					add_ui_element(180, 100, 40, 25, current_state, "Brightness+");

					// Auto-sleep toggle button
					uint16_t sleep_color = app_settings.auto_sleep ? GREEN : RED;
					const char* sleep_text = app_settings.auto_sleep ? "Turn Off" : "Turn On";
					draw_button(&dev, fx16G, 20, 170, 100, 25, sleep_text, sleep_color, WHITE);
					add_ui_element(20, 170, 100, 25, current_state, "AutoSleep Toggle");

					// Back button
					draw_button(&dev, fx16G, 20, 240, 80, 25, "Back", BLUE, WHITE);
					add_ui_element(20, 249, 80, 25, STATE_SETTINGS, "Back"); // 240-20+29=249 (compensate for systematic offset)

					lcdDrawFinish(&dev);
				}
				break;

			case STATE_TOUCH_CALIBRATION:
				if (should_update) {
					clear_ui_elements();

					// Initialize calibration state on first entry
					if (touch_cal_state.current_point == 0 && !touch_cal_state.waiting_for_touch) {
						init_touch_calibration_state();
					}

					lcdFillScreen(&dev, BLACK);
					lcdSetFontDirection(&dev, 0);

					// Title
					color = CYAN;
					strcpy((char *)text, "Touch Calibration");
					lcdDrawString(&dev, fx24G, 20, 30, text, color);

					// Enhanced user-friendly instructions
					color = WHITE;
					strcpy((char *)text, "Touch anywhere near the");
					lcdDrawString(&dev, fx16G, 20, 60, text, color);
					strcpy((char *)text, "target circle. Don't worry");
					lcdDrawString(&dev, fx16G, 20, 80, text, color);
					strcpy((char *)text, "about being precise!");
					lcdDrawString(&dev, fx16G, 20, 100, text, color);

					// Progress indicator
					color = YELLOW;
					sprintf((char *)text, "Point %d of 6", touch_cal_state.current_point + 1);
					lcdDrawString(&dev, fx16G, 20, 130, text, color);

					// Show point description for better user guidance
					color = WHITE;
					if (touch_cal_state.current_point < 4) {
						strcpy((char *)text, "Touch the corner target");
					} else {
						strcpy((char *)text, "Touch the center target");
					}
					lcdDrawString(&dev, fx16G, 20, 150, text, color);

					// Add helpful tip
					color = rgb565(150, 150, 150);
					strcpy((char *)text, "Gray circle shows touch area");
					lcdDrawString(&dev, fx16G, 10, 180, text, color);

					// Cancel button
					draw_button(&dev, fx16G, 150, 220, 70, 25, "Cancel", RED, WHITE);
					add_ui_element(150, 220, 70, 25, STATE_SETTINGS, "Cancel");

					lcdDrawFinish(&dev);
				}

				// Draw enhanced calibration target with large, forgiving visual feedback
				if (touch_cal_state.current_point < 6) {
					uint16_t target_x = touch_cal_state.cal_data.screen_points[touch_cal_state.current_point][0];
					uint16_t target_y = touch_cal_state.cal_data.screen_points[touch_cal_state.current_point][1];

					// Draw large, highly visible target with different colors for corner vs center points
					if (touch_cal_state.current_point < 4) {
						color = RED;  // Corner points in red
					} else {
						color = CYAN; // Center points in cyan for distinction
					}

					// Draw large acceptance area outline (forgiving touch zone)
					lcdDrawCircle(&dev, target_x, target_y, touch_cal_state.acceptance_radius, rgb565(100, 100, 100));

					// Draw large visual target circle (main target)
					lcdDrawCircle(&dev, target_x, target_y, touch_cal_state.target_radius, color);
					lcdDrawCircle(&dev, target_x, target_y, touch_cal_state.target_radius - 2, color);

					// Draw inner target circles for better visibility
					lcdDrawCircle(&dev, target_x, target_y, 25, color);
					lcdDrawCircle(&dev, target_x, target_y, 15, color);

					// Draw crosshair for precise targeting
					lcdDrawLine(&dev, target_x - 30, target_y, target_x + 30, target_y, color);
					lcdDrawLine(&dev, target_x, target_y - 30, target_x, target_y + 30, color);

					// Draw center dot
					lcdDrawFillCircle(&dev, target_x, target_y, 5, color);

					// Add visual feedback if touch was detected but missed target
					if (touch_cal_state.show_retry_prompt) {
						uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
						if (current_time - touch_cal_state.retry_prompt_time < 1500) { // Show for 1.5 seconds
							// Draw gentle retry indication
							color = YELLOW;
							strcpy((char *)text, "Touch anywhere near target");
							lcdDrawString(&dev, fx16G, 10, 200, text, color);
						} else {
							touch_cal_state.show_retry_prompt = false;
						}
					}

					// Add immediate visual feedback when target is hit
					if (touch_cal_state.target_hit) {
						uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
						if (current_time - touch_cal_state.target_hit_time < 500) { // Show for 0.5 seconds
							// Draw success feedback
							lcdDrawFillCircle(&dev, target_x, target_y, touch_cal_state.target_radius, GREEN);
							color = WHITE;
							strcpy((char *)text, "Good!");
							lcdDrawString(&dev, fx16G, target_x - 20, target_y + 60, text, color);
						}
					}

					lcdDrawFinish(&dev);
				}
				break;

			case STATE_SETTINGS_SENSOR:
				if (should_update) {
					clear_ui_elements();

					// LAYER 1: Background
					lcdFillScreen(&dev, BLACK);
					lcdSetFontDirection(&dev, 0);

					// LAYER 2: Title and information text
					color = CYAN;
					strcpy((char *)text, "Sensor Settings");
					lcdDrawString(&dev, fx24G, 30, 30, text, color);

					if (color_sensor_initialized) {
						// Integration time setting
						color = WHITE;
						const char* integration_names[] = {"2.78ms", "27.8ms", "101ms", "175ms", "712ms"};
						sprintf((char *)text, "Integration: %s", integration_names[app_settings.sensor_integration]);
						lcdDrawString(&dev, fx16G, 20, 80, text, color);

						// Gain setting
						const char* gain_names[] = {"1x", "4x", "16x", "64x"};
						sprintf((char *)text, "Gain: %s", gain_names[app_settings.sensor_gain]);
						lcdDrawString(&dev, fx16G, 20, 150, text, color);
					} else {
						color = RED;
						strcpy((char *)text, "Sensor not connected");
						lcdDrawString(&dev, fx16G, 20, 100, text, color);
						strcpy((char *)text, "Connect TCS3430 sensor");
						lcdDrawString(&dev, fx16G, 20, 120, text, color);
						strcpy((char *)text, "to access settings");
						lcdDrawString(&dev, fx16G, 20, 140, text, color);
					}

					// LAYER 3: Interactive buttons (highest z-index)
					if (color_sensor_initialized) {
						// Integration adjustment buttons
						draw_button(&dev, fx16G, 20, 100, 40, 25, "-", RED, WHITE);
						add_ui_element(20, 100, 40, 25, current_state, "Integration-");

						draw_button(&dev, fx16G, 180, 100, 40, 25, "+", GREEN, WHITE);
						add_ui_element(180, 100, 40, 25, current_state, "Integration+");

						// Gain adjustment buttons
						draw_button(&dev, fx16G, 20, 170, 40, 25, "-", RED, WHITE);
						add_ui_element(20, 170, 40, 25, current_state, "Gain-");

						draw_button(&dev, fx16G, 180, 170, 40, 25, "+", GREEN, WHITE);
						add_ui_element(180, 170, 40, 25, current_state, "Gain+");
					}

					// Back button
					draw_button(&dev, fx16G, 20, 240, 80, 25, "Back", BLUE, WHITE);
					add_ui_element(20, 240, 80, 25, STATE_SETTINGS, "Back");

					lcdDrawFinish(&dev);
				}
				break;

			case STATE_ABOUT:
				if (should_update) {
					clear_ui_elements();

					// LAYER 1: Background elements
					lcdFillScreen(&dev, rgb565(20, 20, 40));
					lcdSetFontDirection(&dev, 0);

					// LAYER 2: Title bar
					draw_title_bar(&dev, fx24G, "About", rgb565(200, 100, 0));

					// LAYER 3: Content panel - Keep 220px width, fix Y positioning
					draw_panel(&dev, 10, 55, 220, 170, rgb565(40, 40, 60));

					// LAYER 4: Information text
					uint16_t start_y = 75; // Start below title bar with margin

					color = WHITE;
					strcpy((char *)text, "Professional Color");
					lcdDrawString(&dev, fx16G, 30, start_y, text, color);
					strcpy((char *)text, "Matching Tool v1.0");
					lcdDrawString(&dev, fx16G, 30, start_y + 20, text, color);

					strcpy((char *)text, "Hardware:");
					lcdDrawString(&dev, fx16G, 30, start_y + 50, text, color);
					strcpy((char *)text, "ESP32-S3 + TCS3430");
					lcdDrawString(&dev, fx16G, 30, start_y + 70, text, color);

					strcpy((char *)text, "Features:");
					lcdDrawString(&dev, fx16G, 30, start_y + 100, text, color);
					strcpy((char *)text, "CIE XYZ Colorimetry");
					lcdDrawString(&dev, fx16G, 30, start_y + 120, text, color);
					strcpy((char *)text, "Touch Interface");
					lcdDrawString(&dev, fx16G, 30, start_y + 140, text, color);

					// LAYER 5: Interactive buttons (highest z-index)
					draw_button(&dev, fx16G, 20, 240, 80, 30, "Back", rgb565(0, 100, 200), WHITE);
					add_ui_element(20, 240, 80, 30, STATE_MENU, "Back");

					lcdDrawFinish(&dev);
				}
				break;

			case STATE_ERROR_NO_DEVICE:
				if (should_update) {
					clear_ui_elements();

					// LAYER 1: Background
					lcdFillScreen(&dev, BLACK);
					lcdSetFontDirection(&dev, 0);

					// LAYER 2: Error message text
					color = RED;
					strcpy((char *)text, "Device Not Found");
					lcdDrawString(&dev, fx24G, 20, 50, text, color);

					color = WHITE;
					strcpy((char *)text, "Color scanner required");
					lcdDrawString(&dev, fx16G, 20, 100, text, color);
					strcpy((char *)text, "for this function");
					lcdDrawString(&dev, fx16G, 20, 120, text, color);

					color = YELLOW;
					strcpy((char *)text, "Please connect TCS3430");
					lcdDrawString(&dev, fx16G, 10, 160, text, color);
					strcpy((char *)text, "color sensor and restart");
					lcdDrawString(&dev, fx16G, 10, 180, text, color);

					// LAYER 3: Interactive buttons (highest z-index)
					draw_button(&dev, fx16G, 20, 230, 80, 25, "Back", BLUE, WHITE);
					add_ui_element(20, 230, 80, 25, STATE_MENU, "Back");

					lcdDrawFinish(&dev);
				}
				break;
		}

		vTaskDelay(pdMS_TO_TICKS(100)); // 100ms update rate
	}
}

void app_main(void)
{
	ESP_LOGI(TAG, "Initializing SPIFFS");

	esp_vfs_spiffs_conf_t conf = {
		.base_path = "/spiffs",
		.partition_label = NULL,
		.max_files = 12,
		.format_if_mount_failed = true};

	// Use settings defined above toinitialize and mount SPIFFS filesystem.
	// Note: esp_vfs_spiffs_register is anall-in-one convenience function.
	esp_err_t ret = esp_vfs_spiffs_register(&conf);

	if (ret != ESP_OK)
	{
		if (ret == ESP_FAIL)
		{
			ESP_LOGE(TAG, "Failed to mount or format filesystem");
		}
		else if (ret == ESP_ERR_NOT_FOUND)
		{
			ESP_LOGE(TAG, "Failed to find SPIFFS partition");
		}
		else
		{
			ESP_LOGE(TAG, "Failed to initialize SPIFFS (%s)", esp_err_to_name(ret));
		}
		return;
	}

	size_t total = 0, used = 0;
	ret = esp_spiffs_info(NULL, &total, &used);
	if (ret != ESP_OK)
	{
		ESP_LOGE(TAG, "Failed to get SPIFFS partition information (%s)", esp_err_to_name(ret));
	}
	else
	{
		ESP_LOGI(TAG, "Partition size: total: %d, used: %d", total, used);
	}

	SPIFFS_Directory("/spiffs/");

	// Seed random number generator for demo effects
	srand(time(NULL));

	ESP_LOGI(TAG, "Starting Color Matching Application Task...");
	xTaskCreate(color_matching_app, "ColorMatchingApp", 1024 * 8, NULL, 2, NULL);
}
