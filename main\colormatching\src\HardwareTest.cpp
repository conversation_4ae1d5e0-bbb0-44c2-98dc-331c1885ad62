// Hardware Test Module for ESP32-S3 Color Matching Device
// This file contains hardware testing functions that can be called from the main application
// To use this as the main program, rename this file to main.cpp and rename main_lvgl.cpp to something else

#include <Arduino.h>
#include <Adafruit_NeoPixel.h>
#include <Wire.h>
#include <SPI.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7789.h>
#include <DFRobot_TCS3430.h>
#include <BLEDevice.h>
#include "FS.h"
#include "LittleFS.h"
#include "HardwareConfig.h"

// Pin definitions
#define RGB_LED_PIN 21
#define INT_PIN 15
#define SDA_PIN 8
#define SCL_PIN 9
#define TFT_CS    5
#define TFT_DC    6
#define TFT_RST   4
#define TFT_MOSI  35
#define TFT_SCLK  36
#define BUTTON_SELECT 17
#define BUTTON_UP     18
#define BUTTON_DOWN   19

// Objects
Adafruit_NeoPixel rgb_led(1, R<PERSON>_LED_PIN, NEO_GRB + NEO_KHZ800);
Adafruit_ST7789 tft(TFT_CS, TFT_DC, TFT_RST);
DFRobot_TCS3430 tcs3430;

// Test states
enum TestState {
  TEST_RGB_LED,
  TEST_TFT,
  TEST_BUTTONS,
  TEST_SENSOR,
  TEST_BLE,
  TEST_STORAGE,
  TEST_COMPLETE
};

TestState currentTest = TEST_RGB_LED;
unsigned long testStartTime = 0;
bool testPassed = true;

void testRGBLED();
void testTFT();
void testButtons();
void testColorSensor();
void testBLE();
void testStorage();
void displayResults();

// Hardware test functions - rename to setup() and loop() to use as main program
void hardwareTestSetup() {
  Serial.begin(115200);
  Serial.println("ESP32S3 Hardware Test");
  
  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  
  // Initialize RGB LED
  rgb_led.begin();
  rgb_led.setBrightness(50);
  rgb_led.show();
  
  // Initialize TFT
  tft.init(SCREEN_WIDTH, SCREEN_HEIGHT);
  tft.setRotation(SCREEN_ROTATION);
  
  // Initialize buttons
  pinMode(BUTTON_SELECT, INPUT_PULLUP);
  pinMode(BUTTON_UP, INPUT_PULLUP);
  pinMode(BUTTON_DOWN, INPUT_PULLUP);
  
  // Initialize interrupt pin
  pinMode(INT_PIN, INPUT_PULLUP);
  
  testStartTime = millis();
}

void hardwareTestLoop() {
  switch(currentTest) {
    case TEST_RGB_LED:
      testRGBLED();
      break;
    case TEST_TFT:
      testTFT();
      break;
    case TEST_BUTTONS:
      testButtons();
      break;
    case TEST_SENSOR:
      testColorSensor();
      break;
    case TEST_BLE:
      testBLE();
      break;
    case TEST_STORAGE:
      testStorage();
      break;
    case TEST_COMPLETE:
      displayResults();
      break;
  }
}

void testRGBLED() {
  static int colorIndex = 0;
  static unsigned long lastChange = 0;
  
  if (millis() - lastChange > 1000) {
    lastChange = millis();
    switch(colorIndex) {
      case 0:
        rgb_led.setPixelColor(0, rgb_led.Color(255, 0, 0));
        Serial.println("Testing RED");
        break;
      case 1:
        rgb_led.setPixelColor(0, rgb_led.Color(0, 255, 0));
        Serial.println("Testing GREEN");
        break;
      case 2:
        rgb_led.setPixelColor(0, rgb_led.Color(0, 0, 255));
        Serial.println("Testing BLUE");
        break;
      case 3:
        currentTest = TEST_TFT;
        Serial.println("RGB LED Test Complete");
        return;
    }
    rgb_led.show();
    colorIndex++;
  }
}

void testTFT() {
  static int testPhase = 0;
  static unsigned long lastChange = 0;
  
  if (millis() - lastChange > 1000) {
    lastChange = millis();
    switch(testPhase) {
      case 0:
        tft.fillScreen(ST77XX_RED);
        Serial.println("Testing TFT RED");
        break;
      case 1:
        tft.fillScreen(ST77XX_GREEN);
        Serial.println("Testing TFT GREEN");
        break;
      case 2:
        tft.fillScreen(ST77XX_BLUE);
        Serial.println("Testing TFT BLUE");
        break;
      case 3:
        tft.fillScreen(ST77XX_BLACK);
        tft.setTextColor(ST77XX_WHITE);
        tft.setTextSize(2);
        tft.setCursor(10, 10);
        tft.println("TFT Test");
        Serial.println("Testing TFT Text");
        break;
      case 4:
        currentTest = TEST_BUTTONS;
        Serial.println("TFT Test Complete");
        return;
    }
    testPhase++;
  }
}

void testButtons() {
  static unsigned long startTime = millis();
  tft.fillScreen(ST77XX_BLACK);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Press all buttons:");
  
  static bool selectPressed = false;
  static bool upPressed = false;
  static bool downPressed = false;
  
  if (!digitalRead(BUTTON_SELECT)) selectPressed = true;
  if (!digitalRead(BUTTON_UP)) upPressed = true;
  if (!digitalRead(BUTTON_DOWN)) downPressed = true;
  
  tft.setCursor(10, 40);
  tft.print("Select: "); tft.println(selectPressed ? "OK" : "Wait");
  tft.print("Up: "); tft.println(upPressed ? "OK" : "Wait");
  tft.print("Down: "); tft.println(downPressed ? "OK" : "Wait");
  
  if (selectPressed && upPressed && downPressed) {
    delay(1000);
    currentTest = TEST_SENSOR;
    Serial.println("Button Test Complete");
  }
}

void testColorSensor() {
  static bool sensorInitialized = false;
  static unsigned long startTime = millis();
  
  if (!sensorInitialized) {
    tft.fillScreen(ST77XX_BLACK);
    tft.setTextColor(ST77XX_WHITE);
    tft.setTextSize(2);
    tft.setCursor(10, 10);
    tft.println("Testing Color Sensor");
    
    if (tcs3430.begin()) {
      sensorInitialized = true;
      tcs3430.setIntegrationTime(0x40);
      tcs3430.setALSGain(1);
      Serial.println("Color Sensor Initialized");
    } else {
      tft.setCursor(10, 40);
      tft.println("Sensor Init Failed!");
      testPassed = false;
      delay(2000);
      currentTest = TEST_BLE;
      return;
    }
  }
  
  uint16_t x = tcs3430.getXData();
  uint16_t y = tcs3430.getYData();
  uint16_t z = tcs3430.getZData();
  
  tft.setCursor(10, 40);
  tft.print("X: "); tft.println(x);
  tft.print("Y: "); tft.println(y);
  tft.print("Z: "); tft.println(z);
  
  if (millis() - startTime > 5000) {
    currentTest = TEST_BLE;
    Serial.println("Color Sensor Test Complete");
  }
}

void testBLE() {
  static bool bleInitialized = false;
  static unsigned long startTime = millis();
  
  if (!bleInitialized) {
    tft.fillScreen(ST77XX_BLACK);
    tft.setTextColor(ST77XX_WHITE);
    tft.setTextSize(2);
    tft.setCursor(10, 10);
    tft.println("Testing BLE");
    
    BLEDevice::init("ESP32S3_Test");
    bleInitialized = true;
    Serial.println("BLE Initialized");
  }
  
  if (millis() - startTime > 3000) {
    currentTest = TEST_STORAGE;
    Serial.println("BLE Test Complete");
  }
}

void testStorage() {
  tft.fillScreen(ST77XX_BLACK);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Testing Storage");
  
  if (!LittleFS.begin()) {
    tft.setCursor(10, 40);
    tft.println("Storage Init Failed!");
    testPassed = false;
  } else {
    File f = LittleFS.open("/test.txt", "w");
    if (f) {
      f.println("Test");
      f.close();
      tft.setCursor(10, 40);
      tft.println("Storage OK");
    } else {
      tft.setCursor(10, 40);
      tft.println("Write Failed!");
      testPassed = false;
    }
  }
  
  delay(2000);
  currentTest = TEST_COMPLETE;
  Serial.println("Storage Test Complete");
}

void displayResults() {
  static bool displayed = false;
  if (!displayed) {
    tft.fillScreen(ST77XX_BLACK);
    tft.setTextColor(testPassed ? ST77XX_GREEN : ST77XX_RED);
    tft.setTextSize(2);
    tft.setCursor(10, 10);
    tft.println(testPassed ? "All Tests PASSED" : "Some Tests FAILED");
    tft.setTextColor(ST77XX_WHITE);
    tft.setCursor(10, 40);
    tft.print("Time: ");
    tft.print((millis() - testStartTime) / 1000);
    tft.println("s");
    displayed = true;
    
    // Set LED to indicate test result
    rgb_led.setPixelColor(0, testPassed ? rgb_led.Color(0, 255, 0) : rgb_led.Color(255, 0, 0));
    rgb_led.show();
  }
}
