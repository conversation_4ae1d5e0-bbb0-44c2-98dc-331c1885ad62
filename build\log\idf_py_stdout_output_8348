[1/9] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Pictures\ESP-IDF\01_ESP_IDF_ST7789\build && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffsgen.py 0xf0000 C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/font C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/storage.bin --page-size=256 --obj-name-len=32 --meta-len=4 --use-magic --use-magic-len"
[2/9] Generating ld/sections.ld
[3/9] Performing build step for 'bootloader'
[0/1] Re-running CMake...
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Bootloader project name: "bootloader" version: 1
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main/ld/esp32s3/bootloader.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main/ld/esp32s3/bootloader.rom.ld
-- Components: bootloader bootloader_support efuse esp_app_format esp_bootloader_format esp_common esp_hw_support esp_rom esp_security esp_system esptool_py freertos hal log main micro-ecc newlib partition_table soc spi_flash xtensa
-- Component paths: C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal C:/Espressif/frameworks/esp-idf-v5.4.1/components/log C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/components/micro-ecc C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa
-- Configuring done (6.5s)
-- Generating done (0.2s)
-- Build files have been written to: C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/bootloader
[1/2] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Pictures\ESP-IDF\01_ESP_IDF_ST7789\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/bootloader/bootloader.bin"
Bootloader binary size 0x5220 bytes. 0x2de0 bytes (36%) free.


[4/9] No install step for 'bootloader'
[5/9] Completed 'bootloader'
[6/9] Linking CXX executable st7789.elf
[7/9] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32s3 image...

Merged 2 ELF sections

Successfully created esp32s3 image.

Generated C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/st7789.bin
[8/9] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Pictures\ESP-IDF\01_ESP_IDF_ST7789\build\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 partition --type app C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/partition_table/partition-table.bin C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build/st7789.bin"
st7789.bin binary size 0x56050 bytes. Smallest app partition is 0x100000 bytes. 0xa9fb0 bytes (66%) free.

[8/9] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Espressif\frameworks\esp-idf-v5.4.1\components\esptool_py && C:\Espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Espressif/frameworks/esp-idf-v5.4.1 -D SERIAL_TOOL=C:/Espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=C:/Users/<USER>/Pictures/ESP-IDF/01_ESP_IDF_ST7789/build -P C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32s3 -p COM4 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 bootloader/bootloader.bin 0x10000 st7789.bin 0x8000 partition_table/partition-table.bin 0x110000 storage.bin
esptool.py v4.8.1
Serial port COM4
Connecting...
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)
Crystal is 40MHz
MAC: e8:06:90:93:f9:a4
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x00066fff...
Flash will be erased from 0x00008000 to 0x00008fff...
Flash will be erased from 0x00110000 to 0x001fffff...
SHA digest in image updated
Compressed 21024 bytes to 13389...
Writing at 0x00000000... (100 %)
Wrote 21024 bytes (13389 compressed) at 0x00000000 in 0.5 seconds (effective 352.0 kbit/s)...
Hash of data verified.
Compressed 352336 bytes to 188388...
Writing at 0x00010000... (8 %)
Writing at 0x0001d2e1... (16 %)
Writing at 0x00026e8d... (25 %)
Writing at 0x0002ca46... (33 %)
Writing at 0x00032f02... (41 %)
Writing at 0x000392dd... (50 %)
Writing at 0x0003f454... (58 %)
Writing at 0x0004577e... (66 %)
Writing at 0x0004b5a1... (75 %)
Writing at 0x0005178d... (83 %)
Writing at 0x0005b874... (91 %)
Writing at 0x00062e05... (100 %)
Wrote 352336 bytes (188388 compressed) at 0x00010000 in 3.0 seconds (effective 933.6 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 119...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (119 compressed) at 0x00008000 in 0.1 seconds (effective 233.8 kbit/s)...
Hash of data verified.
Compressed 983040 bytes to 172572...
Writing at 0x00110000... (9 %)
Writing at 0x00114505... (18 %)
Writing at 0x001188e6... (27 %)
Writing at 0x0011cccd... (36 %)
Writing at 0x001211de... (45 %)
Writing at 0x001255ba... (54 %)
Writing at 0x00129d49... (63 %)
Writing at 0x0012e223... (72 %)
Writing at 0x001325ff... (81 %)
Writing at 0x0013a94c... (90 %)
Writing at 0x0014c5f9... (100 %)
Wrote 983040 bytes (172572 compressed) at 0x00110000 in 6.0 seconds (effective 1300.9 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
